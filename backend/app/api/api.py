"""
API路由汇总
"""
from fastapi import APIRouter

from app.api import chat, android

api_router = APIRouter()

# 包含聊天相关路由
api_router.include_router(chat.router, tags=["chat"])

# 包含Android页面分析路由
api_router.include_router(android.router, prefix="/android", tags=["android"])

# 根路径健康检查
@api_router.get("/")
async def root():
    """API根路径"""
    return {
        "message": "UI自动化测试系统 API",
        "version": "1.0.0",
        "status": "running"
    }
