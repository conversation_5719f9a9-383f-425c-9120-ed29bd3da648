"""
聊天API路由
"""
import uuid
from datetime import datetime
from typing import Optional
from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import StreamingResponse
from loguru import logger

from app.models.chat import ChatRequest, ChatResponse
from app.services.autogen_service import autogen_service
from app.utils.sse import sse_manager, SSEResponse

router = APIRouter()


@router.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """
    发送聊天消息（非流式）
    """
    try:
        # 生成或使用现有的会话ID
        conversation_id = request.conversation_id or str(uuid.uuid4())
        
        # 确保AutoGen服务已初始化
        if not autogen_service.runtime:
            await autogen_service.initialize()
        
        # 创建或获取对话会话
        if conversation_id not in autogen_service.agents:
            await autogen_service.create_conversation(conversation_id)
        
        # 发送消息
        await autogen_service.send_message(conversation_id, request.message)
        
        return ChatResponse(
            message="消息已发送，请使用流式接口获取响应",
            conversation_id=conversation_id,
            timestamp=datetime.now(),
            metadata={"stream_endpoint": f"/api/v1/chat/stream/{conversation_id}"}
        )
        
    except Exception as e:
        logger.error(f"聊天接口错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/chat/stream/{conversation_id}")
async def chat_stream(conversation_id: str):
    """
    获取聊天流式响应
    """
    try:
        logger.info(f"开始流式聊天会话: {conversation_id}")
        
        # 创建SSE流生成器
        async def stream_generator():
            async for data in sse_manager.stream_generator(conversation_id):
                yield data
        
        return SSEResponse.create_stream_response(stream_generator())
        
    except Exception as e:
        logger.error(f"流式聊天错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/chat/stream")
async def chat_stream_post(request: ChatRequest):
    """
    发送消息并立即返回流式响应
    """
    try:
        # 生成或使用现有的会话ID
        conversation_id = request.conversation_id or str(uuid.uuid4())
        
        # 确保AutoGen服务已初始化
        if not autogen_service.runtime:
            await autogen_service.initialize()
        
        # 创建流式响应生成器
        async def stream_generator():
            try:
                # 发送初始连接确认
                initial_data = {
                    "type": "connected",
                    "conversation_id": conversation_id,
                    "message": "连接已建立"
                }
                yield SSEResponse.format_sse_data(initial_data, "connected")

                # 先建立SSE连接
                queue = await sse_manager.add_connection(conversation_id)

                # 创建或获取对话会话
                if conversation_id not in autogen_service.agents:
                    await autogen_service.create_conversation(conversation_id)

                # 在后台发送消息（不等待）
                import asyncio
                asyncio.create_task(autogen_service.send_message(conversation_id, request.message))

                # 开始监听SSE数据
                try:
                    while True:
                        try:
                            # 等待数据，设置超时
                            data = await asyncio.wait_for(queue.get(), timeout=30.0)
                            yield data
                        except asyncio.TimeoutError:
                            # 发送心跳包
                            heartbeat = SSEResponse.format_sse_data(
                                {"type": "heartbeat", "timestamp": asyncio.get_event_loop().time()},
                                "heartbeat"
                            )
                            yield heartbeat
                        except Exception as e:
                            logger.error(f"SSE流生成错误: {e}")
                            break
                finally:
                    await sse_manager.remove_connection(conversation_id)
                    
            except Exception as e:
                logger.error(f"流式响应生成错误: {e}")
                error_data = {
                    "type": "error",
                    "content": f"处理请求时出现错误: {str(e)}",
                    "conversation_id": conversation_id
                }
                yield SSEResponse.format_sse_data(error_data, "error")
        
        return SSEResponse.create_stream_response(stream_generator())
        
    except Exception as e:
        logger.error(f"流式聊天POST错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/conversations/{conversation_id}/history")
async def get_conversation_history(conversation_id: str):
    """
    获取对话历史
    """
    try:
        if conversation_id in autogen_service.agents:
            agent = autogen_service.agents[conversation_id]
            return {
                "conversation_id": conversation_id,
                "history": agent.conversation_history
            }
        else:
            return {
                "conversation_id": conversation_id,
                "history": []
            }
    except Exception as e:
        logger.error(f"获取对话历史错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/conversations/{conversation_id}")
async def delete_conversation(conversation_id: str):
    """
    删除对话会话
    """
    try:
        await autogen_service.cleanup_conversation(conversation_id)
        return {"message": f"对话会话 {conversation_id} 已删除"}
    except Exception as e:
        logger.error(f"删除对话会话错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def health_check():
    """
    健康检查
    """
    return {
        "status": "healthy",
        "autogen_initialized": autogen_service.runtime is not None,
        "active_conversations": len(autogen_service.agents)
    }
