"""
Android页面分析API路由
"""
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import JSONResponse
from loguru import logger

from app.models.android import (
    AndroidPageAnalysisRequest, AndroidPageAnalysisResponse,
    AppLaunchRequest, NavigationRequest, AndroidDeviceInfo
)
from app.services.android_analysis_service import android_analysis_service
from app.services.android_device_service import android_device_service
from app.services.page_capture_service import page_capture_service

router = APIRouter()


@router.get("/devices", response_model=List[AndroidDeviceInfo])
async def get_connected_devices():
    """获取已连接的Android设备列表"""
    try:
        devices = await android_device_service.get_connected_devices()
        return devices
    except Exception as e:
        logger.error(f"获取设备列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/analyze", response_model=AndroidPageAnalysisResponse)
async def analyze_android_page(request: AndroidPageAnalysisRequest):
    """
    完整的Android页面分析
    
    执行流程：
    1. 启动指定APP
    2. 导航到对应页面
    3. 获取当前页面DOM树信息
    4. 截取当前页面截图
    5. 将DOM和截图发送给UI_TARS分析
    6. 返回JSON格式的分析结果
    """
    try:
        response = await android_analysis_service.analyze_page_complete(request)
        return response
    except Exception as e:
        logger.error(f"Android页面分析失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/analyze/json")
async def analyze_android_page_json(request: AndroidPageAnalysisRequest):
    """
    完整的Android页面分析（返回格式化的JSON）
    """
    try:
        response = await android_analysis_service.analyze_page_complete(request)
        json_result = android_analysis_service.format_analysis_result_json(response)
        return JSONResponse(content=json_result)
    except Exception as e:
        logger.error(f"Android页面分析失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/analyze/current")
async def analyze_current_page(
    device_id: Optional[str] = Query(None, description="设备ID"),
    analysis_type: str = Query("full", description="分析类型")
):
    """
    分析当前页面（不启动应用，直接分析当前显示的页面）
    """
    try:
        response = await android_analysis_service.analyze_current_page(
            device_id=device_id,
            analysis_type=analysis_type
        )
        json_result = android_analysis_service.format_analysis_result_json(response)
        return JSONResponse(content=json_result)
    except Exception as e:
        logger.error(f"当前页面分析失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/launch")
async def launch_app(request: AppLaunchRequest):
    """启动指定应用"""
    try:
        result = await android_device_service.launch_app(
            package_name=request.package_name,
            activity_name=request.activity_name,
            device_id=request.device_id,
            wait_timeout=request.wait_timeout
        )
        return {"success": True, "data": result}
    except Exception as e:
        logger.error(f"启动应用失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/navigate")
async def navigate_to_page(request: NavigationRequest):
    """执行页面导航操作"""
    try:
        result = await android_device_service.navigate_to_page(
            actions=request.actions,
            device_id=request.device_id
        )
        return {"success": True, "data": result}
    except Exception as e:
        logger.error(f"页面导航失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/capture/dom")
async def capture_page_dom(device_id: Optional[str] = Query(None, description="设备ID")):
    """获取当前页面DOM信息"""
    try:
        dom_info = await page_capture_service.capture_page_dom(device_id)
        return {
            "success": True,
            "data": {
                "total_elements": dom_info.total_elements,
                "timestamp": dom_info.timestamp.isoformat(),
                "elements": [
                    {
                        "resource_id": elem.resource_id,
                        "class_name": elem.class_name,
                        "text": elem.text,
                        "content_desc": elem.content_desc,
                        "bounds": elem.bounds,
                        "clickable": elem.clickable,
                        "xpath": elem.xpath
                    }
                    for elem in dom_info.elements[:50]  # 限制返回前50个元素
                ]
            }
        }
    except Exception as e:
        logger.error(f"捕获DOM失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/capture/screenshot")
async def capture_screenshot(device_id: Optional[str] = Query(None, description="设备ID")):
    """截取当前页面截图"""
    try:
        screenshot_info = await page_capture_service.capture_screenshot(device_id)
        return {
            "success": True,
            "data": {
                "image_path": screenshot_info.image_path,
                "width": screenshot_info.width,
                "height": screenshot_info.height,
                "timestamp": screenshot_info.timestamp.isoformat(),
                "image_base64": screenshot_info.image_base64[:100] + "..." if screenshot_info.image_base64 else None  # 只返回前100个字符作为示例
            }
        }
    except Exception as e:
        logger.error(f"截图失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/history")
async def get_analysis_history(limit: int = Query(10, description="返回记录数量")):
    """获取分析历史记录"""
    try:
        history = await android_analysis_service.get_analysis_history(limit)
        return {"success": True, "data": history}
    except Exception as e:
        logger.error(f"获取分析历史失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/app/current")
async def get_current_app(device_id: Optional[str] = Query(None, description="设备ID")):
    """获取当前运行的应用信息"""
    try:
        device = await android_device_service.get_device(device_id)
        current_app = device.app_current()
        activity = await android_device_service.get_current_activity(device_id)
        
        return {
            "success": True,
            "data": {
                "package": current_app.get("package"),
                "activity": activity,
                "pid": current_app.get("pid")
            }
        }
    except Exception as e:
        logger.error(f"获取当前应用信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def android_health_check():
    """Android服务健康检查"""
    try:
        devices = await android_device_service.get_connected_devices()
        connected_count = len([d for d in devices if d.is_connected])
        
        return {
            "status": "healthy",
            "connected_devices": connected_count,
            "total_devices": len(devices),
            "services": {
                "android_device_service": "running",
                "page_capture_service": "running",
                "ui_tars_service": "running",
                "android_analysis_service": "running"
            }
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }


# 示例请求数据
@router.get("/examples")
async def get_api_examples():
    """获取API使用示例"""
    return {
        "analyze_page_example": {
            "app_launch": {
                "package_name": "com.example.app",
                "activity_name": "com.example.app.MainActivity",
                "device_id": None,
                "wait_timeout": 10
            },
            "navigation": {
                "actions": [
                    {
                        "type": "click",
                        "resource_id": "com.example.app:id/button_login"
                    },
                    {
                        "type": "input",
                        "resource_id": "com.example.app:id/edit_username",
                        "text": "testuser"
                    },
                    {
                        "type": "wait",
                        "time": 2
                    }
                ]
            },
            "analysis_config": {
                "type": "full",
                "custom_prompt": "请特别关注登录相关的UI元素"
            }
        },
        "launch_app_example": {
            "package_name": "com.android.settings",
            "activity_name": None,
            "device_id": None,
            "wait_timeout": 10
        },
        "navigation_example": {
            "actions": [
                {
                    "type": "click",
                    "text": "设置"
                },
                {
                    "type": "swipe",
                    "direction": "up"
                },
                {
                    "type": "click",
                    "coordinates": [500, 300]
                }
            ]
        }
    }
