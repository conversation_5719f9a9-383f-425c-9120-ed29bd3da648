"""
页面DOM和截图捕获服务
"""
import os
import base64
import xml.etree.ElementTree as ET
from typing import Optional, List, Dict, Any
from datetime import datetime
from PIL import Image
import uiautomator2 as u2
from loguru import logger

from app.models.android import PageDOMInfo, ScreenshotInfo, ElementInfo
from app.services.android_device_service import android_device_service


class PageCaptureService:
    """页面捕获服务"""
    
    def __init__(self):
        self.screenshots_dir = "static/screenshots"
        self.ensure_directories()
    
    def ensure_directories(self):
        """确保必要的目录存在"""
        os.makedirs(self.screenshots_dir, exist_ok=True)
        os.makedirs("logs", exist_ok=True)
    
    async def capture_page_dom(self, device_id: Optional[str] = None) -> PageDOMInfo:
        """捕获当前页面的DOM信息"""
        try:
            device = await android_device_service.get_device(device_id)

            # 尝试多种方法获取页面XML源码
            xml_source = None

            # 方法1: 标准dump_hierarchy
            try:
                xml_source = device.dump_hierarchy()
                logger.info("使用标准方法成功获取DOM")
            except Exception as e1:
                logger.warning(f"标准DOM获取失败: {e1}")

                # 方法2: 尝试禁用压缩层次结构
                try:
                    xml_source = device.dump_hierarchy(compressed=False)
                    logger.info("使用非压缩方法成功获取DOM")
                except Exception as e2:
                    logger.warning(f"非压缩DOM获取失败: {e2}")

                    # 方法3: 使用简化的XML获取
                    try:
                        # 重新连接设备
                        import uiautomator2 as u2
                        device = u2.connect(device.serial if hasattr(device, 'serial') else device_id)
                        xml_source = device.dump_hierarchy()
                        logger.info("重连后成功获取DOM")
                    except Exception as e3:
                        logger.error(f"所有DOM获取方法都失败: {e3}")
                        # 创建一个基础的XML结构
                        xml_source = '<?xml version="1.0" encoding="UTF-8"?><hierarchy><node class="android.widget.FrameLayout" text="无法获取DOM结构" /></hierarchy>'

            # 解析XML并提取元素信息
            elements = self._parse_xml_elements(xml_source)

            dom_info = PageDOMInfo(
                xml_source=xml_source,
                elements=elements,
                total_elements=len(elements),
                timestamp=datetime.now()
            )

            logger.info(f"成功捕获页面DOM，共 {len(elements)} 个元素")
            return dom_info

        except Exception as e:
            logger.error(f"捕获页面DOM失败: {e}")
            raise Exception(f"捕获页面DOM失败: {e}")
    
    def _parse_xml_elements(self, xml_source: str) -> List[ElementInfo]:
        """解析XML源码并提取元素信息"""
        elements = []
        
        try:
            root = ET.fromstring(xml_source)
            self._extract_elements_recursive(root, elements, "")
        except Exception as e:
            logger.error(f"解析XML失败: {e}")
        
        return elements
    
    def _extract_elements_recursive(self, element: ET.Element, elements: List[ElementInfo], xpath_prefix: str):
        """递归提取元素信息"""
        try:
            # 构建XPath
            tag_name = element.tag
            xpath = f"{xpath_prefix}/{tag_name}"
            
            # 解析bounds属性
            bounds = None
            bounds_str = element.get('bounds')
            if bounds_str:
                try:
                    # bounds格式: [x1,y1][x2,y2]
                    bounds_parts = bounds_str.replace('[', '').replace(']', ',').split(',')
                    if len(bounds_parts) >= 4:
                        bounds = {
                            'left': int(bounds_parts[0]),
                            'top': int(bounds_parts[1]),
                            'right': int(bounds_parts[2]),
                            'bottom': int(bounds_parts[3])
                        }
                except:
                    pass
            
            # 创建元素信息
            element_info = ElementInfo(
                resource_id=element.get('resource-id'),
                class_name=element.get('class'),
                text=element.get('text'),
                content_desc=element.get('content-desc'),
                bounds=bounds,
                clickable=element.get('clickable') == 'true',
                enabled=element.get('enabled') == 'true',
                focused=element.get('focused') == 'true',
                selected=element.get('selected') == 'true',
                checkable=element.get('checkable') == 'true',
                checked=element.get('checked') == 'true',
                scrollable=element.get('scrollable') == 'true',
                long_clickable=element.get('long-clickable') == 'true',
                password=element.get('password') == 'true',
                xpath=xpath
            )
            
            # 递归处理子元素
            children = []
            for i, child in enumerate(element):
                child_xpath = f"{xpath}[{i+1}]"
                self._extract_elements_recursive(child, children, xpath_prefix + f"/{tag_name}[{i+1}]")
            
            element_info.children = children
            elements.append(element_info)
            
        except Exception as e:
            logger.warning(f"解析元素失败: {e}")
    
    async def capture_screenshot(self, device_id: Optional[str] = None) -> ScreenshotInfo:
        """捕获当前页面截图"""
        try:
            device = await android_device_service.get_device(device_id)
            
            # 生成截图文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            filename = f"screenshot_{timestamp}.png"
            image_path = os.path.join(self.screenshots_dir, filename)
            
            # 截图
            device.screenshot(image_path)
            
            # 获取图片信息
            with Image.open(image_path) as img:
                width, height = img.size
            
            # 转换为Base64（可选）
            image_base64 = None
            try:
                with open(image_path, "rb") as img_file:
                    image_base64 = base64.b64encode(img_file.read()).decode('utf-8')
            except Exception as e:
                logger.warning(f"转换截图为Base64失败: {e}")
            
            screenshot_info = ScreenshotInfo(
                image_path=image_path,
                image_base64=image_base64,
                width=width,
                height=height,
                timestamp=datetime.now()
            )
            
            logger.info(f"成功捕获截图: {image_path} ({width}x{height})")
            return screenshot_info
            
        except Exception as e:
            logger.error(f"捕获截图失败: {e}")
            raise Exception(f"捕获截图失败: {e}")
    
    async def capture_page_info(self, device_id: Optional[str] = None) -> tuple[PageDOMInfo, ScreenshotInfo]:
        """同时捕获页面DOM和截图"""
        try:
            # 并发捕获DOM和截图
            import asyncio
            
            dom_task = self.capture_page_dom(device_id)
            screenshot_task = self.capture_screenshot(device_id)
            
            dom_info, screenshot_info = await asyncio.gather(dom_task, screenshot_task)
            
            logger.info("成功捕获页面DOM和截图")
            return dom_info, screenshot_info
            
        except Exception as e:
            logger.error(f"捕获页面信息失败: {e}")
            raise Exception(f"捕获页面信息失败: {e}")
    
    def get_interactive_elements(self, elements: List[ElementInfo]) -> List[ElementInfo]:
        """获取可交互的元素"""
        interactive_elements = []
        
        def check_element(element: ElementInfo):
            if (element.clickable or element.long_clickable or 
                element.checkable or element.scrollable or
                element.class_name in ['android.widget.Button', 'android.widget.EditText', 
                                     'android.widget.ImageButton', 'android.widget.CheckBox']):
                interactive_elements.append(element)
            
            for child in element.children:
                check_element(child)
        
        for element in elements:
            check_element(element)
        
        return interactive_elements
    
    def get_text_elements(self, elements: List[ElementInfo]) -> List[ElementInfo]:
        """获取包含文本的元素"""
        text_elements = []
        
        def check_element(element: ElementInfo):
            if element.text and element.text.strip():
                text_elements.append(element)
            
            for child in element.children:
                check_element(child)
        
        for element in elements:
            check_element(element)
        
        return text_elements
    
    def cleanup_old_screenshots(self, max_age_hours: int = 24):
        """清理旧的截图文件"""
        try:
            import time
            current_time = time.time()
            max_age_seconds = max_age_hours * 3600
            
            for filename in os.listdir(self.screenshots_dir):
                if filename.endswith('.png'):
                    file_path = os.path.join(self.screenshots_dir, filename)
                    file_age = current_time - os.path.getctime(file_path)
                    
                    if file_age > max_age_seconds:
                        os.remove(file_path)
                        logger.info(f"删除旧截图: {filename}")
                        
        except Exception as e:
            logger.warning(f"清理旧截图失败: {e}")


# 全局页面捕获服务实例
page_capture_service = PageCaptureService()
