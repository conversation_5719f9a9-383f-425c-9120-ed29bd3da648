"""
AutoGen 0.5.7 集成服务
基于官方教程实现与大模型对话的智能体
"""
import asyncio
import uuid
from typing import Dict, Any, Optional, AsyncGenerator
from dataclasses import dataclass

from autogen_core import (
    RoutedAgent,
    MessageContext,
    message_handler,
    AgentId,
    SingleThreadedAgentRuntime,
    type_subscription,
    TopicId
)
from pydantic import BaseModel
from loguru import logger
from openai import OpenAI

from app.core.config import settings
from app.utils.sse import sse_manager


class ChatMessage(BaseModel):
    """聊天消息模型"""
    content: str
    role: str = "user"
    conversation_id: Optional[str] = None


class AssistantResponse(BaseModel):
    """助手响应模型"""
    content: str
    conversation_id: str
    is_complete: bool = False


@dataclass
class UserMessage:
    """用户消息数据类"""
    content: str
    conversation_id: str


@dataclass
class AssistantMessage:
    """助手消息数据类"""
    content: str
    conversation_id: str
    is_complete: bool = False


@type_subscription(topic_type="user_input")
class ChatAssistantAgent(RoutedAgent):
    """聊天助手智能体"""
    
    def __init__(self, conversation_id: str):
        super().__init__(f"聊天助手-{conversation_id}")
        self.conversation_id = conversation_id
        self.conversation_history = []

        # 初始化OpenAI客户端
        self.openai_client = OpenAI(
            api_key=settings.OPENAI_API_KEY,
            base_url=settings.OPENAI_API_BASE
        )
    
    @message_handler
    async def handle_user_message(self, message: UserMessage, ctx: MessageContext) -> None:
        """处理用户消息"""
        try:
            logger.info(f"收到用户消息: {message.content}")
            
            # 添加到对话历史
            self.conversation_history.append({
                "role": "user",
                "content": message.content
            })
            
            # 生成AI响应
            logger.info(f"开始生成AI响应...")
            response_content = await self._generate_response(message.content)
            logger.info(f"AI响应生成完成，长度: {len(response_content)}")

            # 流式发送响应
            logger.info(f"开始流式发送响应...")
            await self._stream_response(response_content, message.conversation_id)
            logger.info(f"流式响应发送完成")
            
        except Exception as e:
            logger.error(f"处理用户消息时出错: {e}")
            await self._send_error_response(message.conversation_id, str(e))
    
    async def _generate_response(self, user_input: str) -> str:
        """生成AI响应"""
        try:
            # 构建消息历史
            messages = [
                {
                    "role": "system",
                    "content": "你是一个专业的UI自动化测试助手。你可以帮助用户：\n1. 生成Android和Web自动化测试脚本\n2. 分析测试用例和测试策略\n3. 优化测试流程\n4. 解决自动化测试中的问题\n5. 提供测试最佳实践建议\n\n请用中文回答，保持专业和友好的语气。"
                }
            ]

            # 添加对话历史（最近5轮对话）
            recent_history = self.conversation_history[-10:] if len(self.conversation_history) > 10 else self.conversation_history
            messages.extend(recent_history)

            # 添加当前用户输入
            messages.append({"role": "user", "content": user_input})

            # 调用OpenAI API
            response = self.openai_client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=messages,
                max_tokens=1000,
                temperature=0.7
            )

            return response.choices[0].message.content

        except Exception as e:
            logger.error(f"AI响应生成失败: {e}")
            # 降级到简单响应
            return f"抱歉，我暂时无法处理您的请求。作为UI自动化测试助手，我可以帮助您生成测试脚本、分析测试用例等。请稍后再试或描述您的具体需求。"
    
    async def _stream_response(self, content: str, conversation_id: str):
        """流式发送响应"""
        logger.info(f"开始流式发送，内容长度: {len(content)}, 会话ID: {conversation_id}")

        # 将响应分块发送，模拟流式输出
        words = content.split()
        current_content = ""
        logger.info(f"分割为 {len(words)} 个词")
        
        for i, word in enumerate(words):
            current_content += word + " "
            
            # 发送当前内容块
            chunk_data = {
                "type": "chunk",
                "content": current_content.strip(),
                "conversation_id": conversation_id,
                "is_complete": False
            }
            
            await sse_manager.send_to_connection(
                conversation_id, 
                chunk_data, 
                "message_chunk"
            )
            
            # 模拟打字延迟
            await asyncio.sleep(0.1)
        
        # 发送完成标记
        final_data = {
            "type": "complete",
            "content": current_content.strip(),
            "conversation_id": conversation_id,
            "is_complete": True
        }
        
        await sse_manager.send_to_connection(
            conversation_id,
            final_data,
            "message_complete"
        )
        
        # 添加到对话历史
        self.conversation_history.append({
            "role": "assistant",
            "content": current_content.strip()
        })
    
    async def _send_error_response(self, conversation_id: str, error_message: str):
        """发送错误响应"""
        error_data = {
            "type": "error",
            "content": f"抱歉，处理您的请求时出现了错误: {error_message}",
            "conversation_id": conversation_id,
            "is_complete": True
        }
        
        await sse_manager.send_to_connection(
            conversation_id,
            error_data,
            "error"
        )


class AutoGenService:
    """AutoGen服务管理类"""
    
    def __init__(self):
        self.runtime = None
        self.agents: Dict[str, ChatAssistantAgent] = {}
        self._runtime_started = False
    
    async def initialize(self):
        """初始化AutoGen运行时"""
        try:
            if self.runtime is None:
                self.runtime = SingleThreadedAgentRuntime()
                logger.info("AutoGen运行时初始化成功")
        except Exception as e:
            logger.error(f"AutoGen运行时初始化失败: {e}")
            raise
    
    async def create_conversation(self, conversation_id: Optional[str] = None) -> str:
        """创建新的对话会话"""
        if not conversation_id:
            conversation_id = str(uuid.uuid4())
        
        try:
            # 创建智能体
            agent = ChatAssistantAgent(conversation_id)
            
            # 注册智能体到运行时
            await ChatAssistantAgent.register(
                self.runtime,
                f"assistant_{conversation_id}",
                lambda: agent
            )
            
            self.agents[conversation_id] = agent
            
            # 启动运行时（如果还未启动）
            if not self._runtime_started:
                self.runtime.start()
                self._runtime_started = True
            
            logger.info(f"创建对话会话: {conversation_id}")
            return conversation_id
            
        except Exception as e:
            logger.error(f"创建对话会话失败: {e}")
            raise
    
    async def send_message(self, conversation_id: str, message: str):
        """发送消息到指定对话"""
        try:
            if conversation_id not in self.agents:
                await self.create_conversation(conversation_id)

            # 直接调用智能体处理消息（绕过AutoGen的消息系统）
            agent = self.agents[conversation_id]

            # 创建用户消息
            user_message = UserMessage(
                content=message,
                conversation_id=conversation_id
            )

            # 直接调用消息处理方法
            await agent.handle_user_message(user_message, None)

            logger.info(f"消息已发送到会话 {conversation_id}: {message}")

        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            raise
    
    async def cleanup_conversation(self, conversation_id: str):
        """清理对话会话"""
        if conversation_id in self.agents:
            del self.agents[conversation_id]
            logger.info(f"清理对话会话: {conversation_id}")


# 全局AutoGen服务实例
autogen_service = AutoGenService()
