"""
Android设备管理服务
"""
import asyncio
import subprocess
import time
from typing import List, Optional, Dict, Any
import uiautomator2 as u2
from adbutils import adb
from loguru import logger

from app.core.config import settings
from app.models.android import AndroidDeviceInfo


class AndroidDeviceService:
    """Android设备管理服务"""
    
    def __init__(self):
        self.connected_devices: Dict[str, u2.Device] = {}
        self.device_info_cache: Dict[str, AndroidDeviceInfo] = {}
    
    async def get_connected_devices(self) -> List[AndroidDeviceInfo]:
        """获取已连接的Android设备列表"""
        try:
            devices = []
            adb_devices = adb.device_list()
            
            for device in adb_devices:
                device_id = device.serial
                
                try:
                    # 获取设备信息
                    d = u2.connect(device_id)
                    device_info = await self._get_device_info(d, device_id)
                    devices.append(device_info)
                    
                    # 缓存设备连接
                    self.connected_devices[device_id] = d
                    self.device_info_cache[device_id] = device_info
                    
                except Exception as e:
                    logger.warning(f"无法连接到设备 {device_id}: {e}")
                    devices.append(AndroidDeviceInfo(
                        device_id=device_id,
                        is_connected=False
                    ))
            
            logger.info(f"发现 {len(devices)} 个Android设备")
            return devices
            
        except Exception as e:
            logger.error(f"获取设备列表失败: {e}")
            return []
    
    async def _get_device_info(self, device: u2.Device, device_id: str) -> AndroidDeviceInfo:
        """获取设备详细信息"""
        try:
            info = device.info
            
            return AndroidDeviceInfo(
                device_id=device_id,
                device_name=info.get('productName', 'Unknown'),
                platform_version=info.get('version', 'Unknown'),
                screen_size=f"{info.get('displayWidth', 0)}x{info.get('displayHeight', 0)}",
                is_connected=True
            )
        except Exception as e:
            logger.error(f"获取设备信息失败: {e}")
            return AndroidDeviceInfo(
                device_id=device_id,
                is_connected=False
            )
    
    async def get_device(self, device_id: Optional[str] = None) -> u2.Device:
        """获取设备连接"""
        if device_id is None:
            # 获取第一个可用设备
            devices = await self.get_connected_devices()
            if not devices:
                raise Exception("没有找到可用的Android设备")
            
            connected_devices = [d for d in devices if d.is_connected]
            if not connected_devices:
                raise Exception("没有已连接的Android设备")
            
            device_id = connected_devices[0].device_id
        
        if device_id not in self.connected_devices:
            # 尝试连接设备
            try:
                device = u2.connect(device_id)
                self.connected_devices[device_id] = device
                logger.info(f"成功连接到设备: {device_id}")
            except Exception as e:
                raise Exception(f"无法连接到设备 {device_id}: {e}")
        
        return self.connected_devices[device_id]
    
    async def launch_app(self, package_name: str, activity_name: Optional[str] = None, 
                        device_id: Optional[str] = None, wait_timeout: int = 10) -> Dict[str, Any]:
        """启动指定应用"""
        try:
            device = await self.get_device(device_id)
            
            # 检查应用是否已安装
            if not device.app_info(package_name):
                raise Exception(f"应用 {package_name} 未安装")
            
            # 启动应用
            if activity_name:
                device.app_start(package_name, activity_name)
            else:
                device.app_start(package_name)
            
            # 等待应用启动
            await asyncio.sleep(2)
            
            # 验证应用是否启动成功
            current_app = device.app_current()
            if current_app['package'] != package_name:
                logger.warning(f"当前应用 {current_app['package']} 与目标应用 {package_name} 不匹配")
            
            logger.info(f"成功启动应用: {package_name}")
            
            return {
                "success": True,
                "package_name": package_name,
                "activity_name": activity_name,
                "current_app": current_app,
                "device_id": device.serial if hasattr(device, 'serial') else device_id
            }
            
        except Exception as e:
            logger.error(f"启动应用失败: {e}")
            raise Exception(f"启动应用 {package_name} 失败: {e}")
    
    async def navigate_to_page(self, actions: List[Dict[str, Any]], 
                              device_id: Optional[str] = None) -> Dict[str, Any]:
        """执行导航操作到达指定页面"""
        try:
            device = await self.get_device(device_id)
            executed_actions = []
            
            for i, action in enumerate(actions):
                try:
                    action_type = action.get('type')
                    
                    if action_type == 'click':
                        # 点击操作
                        if 'resource_id' in action:
                            device(resourceId=action['resource_id']).click()
                        elif 'text' in action:
                            device(text=action['text']).click()
                        elif 'coordinates' in action:
                            x, y = action['coordinates']
                            device.click(x, y)
                        else:
                            raise Exception("点击操作缺少必要参数")
                    
                    elif action_type == 'input':
                        # 输入操作
                        text = action.get('text', '')
                        if 'resource_id' in action:
                            device(resourceId=action['resource_id']).set_text(text)
                        elif 'selector' in action:
                            device(**action['selector']).set_text(text)
                        else:
                            raise Exception("输入操作缺少选择器")
                    
                    elif action_type == 'swipe':
                        # 滑动操作
                        direction = action.get('direction', 'up')
                        if direction == 'up':
                            device.swipe_ext("up")
                        elif direction == 'down':
                            device.swipe_ext("down")
                        elif direction == 'left':
                            device.swipe_ext("left")
                        elif direction == 'right':
                            device.swipe_ext("right")
                    
                    elif action_type == 'wait':
                        # 等待操作
                        wait_time = action.get('time', 1)
                        await asyncio.sleep(wait_time)
                    
                    else:
                        logger.warning(f"未知的操作类型: {action_type}")
                        continue
                    
                    executed_actions.append({
                        "index": i,
                        "action": action,
                        "success": True
                    })
                    
                    # 操作间隔
                    await asyncio.sleep(0.5)
                    
                except Exception as e:
                    logger.error(f"执行操作 {i} 失败: {e}")
                    executed_actions.append({
                        "index": i,
                        "action": action,
                        "success": False,
                        "error": str(e)
                    })
            
            logger.info(f"导航操作完成，执行了 {len(executed_actions)} 个操作")
            
            return {
                "success": True,
                "executed_actions": executed_actions,
                "total_actions": len(actions),
                "device_id": device.serial if hasattr(device, 'serial') else device_id
            }
            
        except Exception as e:
            logger.error(f"页面导航失败: {e}")
            raise Exception(f"页面导航失败: {e}")
    
    async def get_current_activity(self, device_id: Optional[str] = None) -> str:
        """获取当前Activity"""
        try:
            device = await self.get_device(device_id)
            current_app = device.app_current()
            return current_app.get('activity', 'Unknown')
        except Exception as e:
            logger.error(f"获取当前Activity失败: {e}")
            return "Unknown"
    
    async def is_app_running(self, package_name: str, device_id: Optional[str] = None) -> bool:
        """检查应用是否正在运行"""
        try:
            device = await self.get_device(device_id)
            current_app = device.app_current()
            return current_app.get('package') == package_name
        except Exception as e:
            logger.error(f"检查应用运行状态失败: {e}")
            return False


# 全局Android设备服务实例
android_device_service = AndroidDeviceService()
