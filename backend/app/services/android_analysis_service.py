"""
Android页面分析智能体服务
"""
import time
import json
from typing import Optional, Dict, Any, List
from datetime import datetime
from loguru import logger

from app.models.android import (
    AndroidPageAnalysisRequest, AndroidPageAnalysisResponse,
    AppLaunchRequest, NavigationRequest, AndroidDeviceInfo
)
from app.services.android_device_service import android_device_service
from app.services.page_capture_service import page_capture_service
from app.services.ui_tars_service import ui_tars_service


class AndroidAnalysisService:
    """Android页面分析智能体服务"""
    
    def __init__(self):
        self.analysis_history = []
    
    async def analyze_page_complete(self, request: AndroidPageAnalysisRequest) -> AndroidPageAnalysisResponse:
        """完整的Android页面分析流程"""
        start_time = time.time()
        
        try:
            logger.info("开始Android页面分析流程")
            
            # 1. 获取设备信息
            device_info = await self._get_device_info(request.app_launch.device_id)
            
            # 2. 启动指定APP
            app_info = await self._launch_app(request.app_launch)
            
            # 3. 导航到对应页面（如果需要）
            navigation_result = None
            if request.navigation:
                navigation_result = await self._navigate_to_page(request.navigation)
            
            # 4. 等待页面稳定
            await self._wait_for_page_stable()
            
            # 5. 获取当前页面DOM树信息和截图
            try:
                dom_info, screenshot_info = await page_capture_service.capture_page_info(request.app_launch.device_id)
            except Exception as e:
                logger.warning(f"同时捕获DOM和截图失败，尝试分别捕获: {e}")

                # 分别尝试捕获
                dom_info = None
                screenshot_info = None

                try:
                    screenshot_info = await page_capture_service.capture_screenshot(request.app_launch.device_id)
                    logger.info("截图捕获成功")
                except Exception as e2:
                    logger.error(f"截图捕获失败: {e2}")

                try:
                    dom_info = await page_capture_service.capture_page_dom(request.app_launch.device_id)
                    logger.info("DOM捕获成功")
                except Exception as e3:
                    logger.error(f"DOM捕获失败: {e3}")
                    # 如果DOM捕获失败，创建一个基础的DOM信息
                    from app.models.android import PageDOMInfo
                    dom_info = PageDOMInfo(
                        xml_source='<?xml version="1.0" encoding="UTF-8"?><hierarchy><node class="android.widget.FrameLayout" text="DOM获取失败" /></hierarchy>',
                        elements=[],
                        total_elements=0,
                        timestamp=datetime.now()
                    )
            
            # 7. 将DOM和截图发送给UI_TARS分析
            analysis_result = await ui_tars_service.analyze_page(
                dom_info=dom_info,
                screenshot_info=screenshot_info,
                analysis_type=request.analysis_config.get("type", "full"),
                custom_prompt=request.analysis_config.get("custom_prompt")
            )
            
            # 8. 构建完整响应
            execution_time = time.time() - start_time
            
            response = AndroidPageAnalysisResponse(
                success=True,
                message="页面分析完成",
                device_info=device_info,
                app_info=app_info,
                dom_info=dom_info,
                screenshot_info=screenshot_info,
                analysis_result=analysis_result,
                execution_time=execution_time,
                timestamp=datetime.now()
            )
            
            # 记录分析历史
            self._record_analysis_history(request, response)
            
            logger.info(f"Android页面分析完成，耗时 {execution_time:.2f} 秒")
            return response
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Android页面分析失败: {e}")
            
            return AndroidPageAnalysisResponse(
                success=False,
                message=f"页面分析失败: {str(e)}",
                execution_time=execution_time,
                timestamp=datetime.now()
            )
    
    async def _get_device_info(self, device_id: Optional[str] = None) -> AndroidDeviceInfo:
        """获取设备信息"""
        try:
            devices = await android_device_service.get_connected_devices()
            
            if device_id:
                # 查找指定设备
                for device in devices:
                    if device.device_id == device_id:
                        return device
                raise Exception(f"未找到设备: {device_id}")
            else:
                # 使用第一个可用设备
                connected_devices = [d for d in devices if d.is_connected]
                if not connected_devices:
                    raise Exception("没有可用的Android设备")
                return connected_devices[0]
                
        except Exception as e:
            logger.error(f"获取设备信息失败: {e}")
            raise
    
    async def _launch_app(self, app_launch: AppLaunchRequest) -> Dict[str, Any]:
        """启动应用"""
        try:
            logger.info(f"启动应用: {app_launch.package_name}")
            
            app_info = await android_device_service.launch_app(
                package_name=app_launch.package_name,
                activity_name=app_launch.activity_name,
                device_id=app_launch.device_id,
                wait_timeout=app_launch.wait_timeout
            )
            
            return app_info
            
        except Exception as e:
            logger.error(f"启动应用失败: {e}")
            raise
    
    async def _navigate_to_page(self, navigation: NavigationRequest) -> Dict[str, Any]:
        """导航到指定页面"""
        try:
            logger.info(f"执行页面导航，共 {len(navigation.actions)} 个操作")
            
            navigation_result = await android_device_service.navigate_to_page(
                actions=navigation.actions,
                device_id=navigation.device_id
            )
            
            return navigation_result
            
        except Exception as e:
            logger.error(f"页面导航失败: {e}")
            raise
    
    async def _wait_for_page_stable(self, wait_time: float = 2.0):
        """等待页面稳定"""
        import asyncio
        logger.info(f"等待页面稳定 {wait_time} 秒")
        await asyncio.sleep(wait_time)
    
    def _record_analysis_history(self, request: AndroidPageAnalysisRequest, 
                                response: AndroidPageAnalysisResponse):
        """记录分析历史"""
        try:
            history_record = {
                "timestamp": response.timestamp.isoformat(),
                "package_name": request.app_launch.package_name,
                "device_id": response.device_info.device_id if response.device_info else None,
                "success": response.success,
                "execution_time": response.execution_time,
                "elements_count": len(response.analysis_result.ui_elements) if response.analysis_result else 0,
                "confidence_score": response.analysis_result.confidence_score if response.analysis_result else 0.0
            }
            
            self.analysis_history.append(history_record)
            
            # 保持历史记录数量限制
            if len(self.analysis_history) > 100:
                self.analysis_history = self.analysis_history[-100:]
                
        except Exception as e:
            logger.warning(f"记录分析历史失败: {e}")
    
    async def get_analysis_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取分析历史"""
        return self.analysis_history[-limit:]
    
    async def analyze_current_page(self, device_id: Optional[str] = None, 
                                  analysis_type: str = "full") -> AndroidPageAnalysisResponse:
        """分析当前页面（不启动应用，直接分析当前显示的页面）"""
        start_time = time.time()
        
        try:
            logger.info("开始分析当前页面")
            
            # 1. 获取设备信息
            device_info = await self._get_device_info(device_id)
            
            # 2. 获取当前应用信息
            device = await android_device_service.get_device(device_id)
            current_app = device.app_current()
            
            # 3. 捕获页面信息
            dom_info, screenshot_info = await page_capture_service.capture_page_info(device_id)
            
            # 4. UI_TARS分析
            analysis_result = await ui_tars_service.analyze_page(
                dom_info=dom_info,
                screenshot_info=screenshot_info,
                analysis_type=analysis_type
            )
            
            execution_time = time.time() - start_time
            
            response = AndroidPageAnalysisResponse(
                success=True,
                message="当前页面分析完成",
                device_info=device_info,
                app_info=current_app,
                dom_info=dom_info,
                screenshot_info=screenshot_info,
                analysis_result=analysis_result,
                execution_time=execution_time,
                timestamp=datetime.now()
            )
            
            logger.info(f"当前页面分析完成，耗时 {execution_time:.2f} 秒")
            return response
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"当前页面分析失败: {e}")
            
            return AndroidPageAnalysisResponse(
                success=False,
                message=f"当前页面分析失败: {str(e)}",
                execution_time=execution_time,
                timestamp=datetime.now()
            )
    
    def format_analysis_result_json(self, response: AndroidPageAnalysisResponse) -> Dict[str, Any]:
        """将分析结果格式化为JSON输出"""
        if not response.success:
            return {
                "success": False,
                "error": response.message,
                "timestamp": response.timestamp.isoformat()
            }
        
        # 构建完整的JSON结果
        result = {
            "success": True,
            "timestamp": response.timestamp.isoformat(),
            "execution_time": response.execution_time,
            "device_info": {
                "device_id": response.device_info.device_id,
                "device_name": response.device_info.device_name,
                "platform_version": response.device_info.platform_version,
                "screen_size": response.device_info.screen_size
            } if response.device_info else None,
            "app_info": response.app_info,
            "page_analysis": {
                "page_title": response.analysis_result.page_title,
                "page_type": response.analysis_result.page_type,
                "main_content": response.analysis_result.main_content,
                "analysis_summary": response.analysis_result.analysis_summary,
                "confidence_score": response.analysis_result.confidence_score,
                "ui_elements": [
                    {
                        "element_type": elem.element_type,
                        "text_content": elem.text_content,
                        "description": elem.description,
                        "position": elem.position,
                        "functionality": elem.functionality,
                        "interaction_type": elem.interaction_type,
                        "importance_score": elem.importance_score,
                        "attributes": elem.attributes
                    }
                    for elem in response.analysis_result.ui_elements
                ],
                "layout_structure": response.analysis_result.layout_structure,
                "accessibility_info": response.analysis_result.accessibility_info
            } if response.analysis_result else None,
            "technical_info": {
                "dom_elements_count": response.dom_info.total_elements if response.dom_info else 0,
                "screenshot_path": response.screenshot_info.image_path if response.screenshot_info else None,
                "screenshot_size": {
                    "width": response.screenshot_info.width,
                    "height": response.screenshot_info.height
                } if response.screenshot_info else None
            }
        }
        
        return result


# 全局Android分析服务实例
android_analysis_service = AndroidAnalysisService()
