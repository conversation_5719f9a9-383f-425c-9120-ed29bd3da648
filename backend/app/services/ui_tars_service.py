"""
UI_TARS模型分析服务
基于AutoGen框架的标准AssistantAgent实现Android页面UI分析智能体
"""
import json
import base64
import uuid
import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from PIL import Image
from loguru import logger

# AutoGen框架导入
import autogen
from autogen import AssistantAgent, UserProxyAgent, ConversableAgent

from app.core.config import settings
from app.models.android import (
    PageDOMInfo, ScreenshotInfo, UIAnalysisRequest,
    PageAnalysisResult, UIElement
)


class UITarsAnalysisAgent(AssistantAgent):
    """
    UI_TARS分析智能体
    基于AutoGen标准AssistantAgent实现的Android页面UI分析智能体

    该智能体专门负责：
    1. 接收页面截图和DOM信息
    2. 调用UI_TARS模型进行分析
    3. 解析和结构化分析结果
    4. 提供标准的AutoGen对话能力
    """

    def __init__(
        self,
        name: str = "ui_tars_analyzer",
        system_message: Optional[str] = None,
        llm_config: Optional[Dict[str, Any]] = None,
        max_consecutive_auto_reply: int = 1,
        human_input_mode: str = "NEVER",
        code_execution_config: bool = False,
        **kwargs
    ):
        """
        初始化UI_TARS分析智能体

        Args:
            name: 智能体名称
            system_message: 系统消息
            llm_config: LLM配置
            max_consecutive_auto_reply: 最大连续自动回复次数
            human_input_mode: 人工输入模式
            code_execution_config: 代码执行配置
            **kwargs: 其他参数
        """

        # 获取默认配置
        if llm_config is None:
            llm_config = self._get_default_llm_config()

        if system_message is None:
            system_message = self._get_default_system_message()

        # 调用父类初始化
        super().__init__(
            name=name,
            system_message=system_message,
            llm_config=llm_config,
            max_consecutive_auto_reply=max_consecutive_auto_reply,
            human_input_mode=human_input_mode,
            code_execution_config=code_execution_config,
            **kwargs
        )

        # UI_TARS特定配置
        self.ui_tars_api_key = settings.UI_TARS_API_KEY
        self.ui_tars_base_url = settings.UI_TARS_BASE_URL
        self.ui_tars_model = settings.UI_TARS_MODEL

        # 分析历史和性能监控
        self.analysis_history: List[Dict[str, Any]] = []
        self.performance_metrics: Dict[str, Any] = {}

        # 注册UI分析回复函数
        self.register_reply(
            trigger=self._is_ui_analysis_request,
            reply_func=self._handle_ui_analysis_request,
            position=0
        )

        logger.info(f"UI_TARS分析智能体初始化完成: {name}")
        logger.info(f"使用模型: {self.ui_tars_model}")
        logger.info(f"API端点: {self.ui_tars_base_url}")

    def _get_default_llm_config(self) -> Dict[str, Any]:
        """获取默认LLM配置"""
        return {
            "config_list": [
                {
                    "model": settings.UI_TARS_MODEL,
                    "api_key": settings.UI_TARS_API_KEY,
                    "base_url": settings.UI_TARS_BASE_URL,
                    # 移除api_type参数，因为AutoGen不支持
                }
            ],
            "temperature": 0.1,
            "max_tokens": 4000,
            "timeout": 120,
        }

    def _get_default_system_message(self) -> str:
        """获取默认系统消息"""
        return """你是一个专业的Android UI界面分析专家智能体。

## 核心能力
- 分析Android应用界面的UI元素和布局结构
- 识别用户交互元素（按钮、输入框、菜单等）
- 理解页面功能和用户流程
- 提供结构化的分析结果

## 工作流程
1. 接收页面截图和DOM结构信息
2. 分析界面元素的类型、位置和功能
3. 识别页面的主要内容和交互模式
4. 生成结构化的JSON格式分析报告

## 输出要求
- 必须返回有效的JSON格式结果
- 包含页面标题、类型、主要内容
- 详细的UI元素列表和属性
- 布局结构和可访问性信息
- 分析置信度评分

请始终保持专业、准确、结构化的分析方式。"""

    def _is_ui_analysis_request(self, recipient, messages, sender, config) -> bool:
        """判断是否为UI分析请求"""
        if not messages:
            return False

        last_message = messages[-1]
        content = last_message.get("content", "").lower()

        # 检查消息内容和上下文
        ui_keywords = [
            "ui_analysis", "页面分析", "界面分析", "screenshot", "dom_info",
            "analyze_page", "ui元素", "android页面"
        ]

        return any(keyword in content for keyword in ui_keywords)

    async def _handle_ui_analysis_request(self, recipient, messages, sender, config) -> tuple[bool, str]:
        """处理UI分析请求"""
        try:
            if not messages:
                return True, "没有收到分析请求消息"

            last_message = messages[-1]
            content = last_message.get("content", "")

            # 尝试从消息中解析分析参数
            analysis_params = self._parse_analysis_params(content)

            if not analysis_params:
                return True, """我是专业的Android UI界面分析智能体。

要进行UI分析，请提供以下信息：
1. 页面截图（base64编码或文件路径）
2. DOM结构信息（可选）
3. 分析类型（full/detailed/summary）

示例格式：
```
ui_analysis:
{
  "screenshot_base64": "iVBORw0KGgoAAAANSUhEUgAA...",
  "dom_info": {...},
  "analysis_type": "full"
}
```

我将为您提供详细的UI元素分析和页面结构解析。"""

            # 执行UI分析
            analysis_result = await self._perform_ui_analysis(analysis_params)

            # 格式化响应
            response = self._format_analysis_response(analysis_result)

            # 记录分析历史
            self._record_analysis(analysis_params, analysis_result)

            return True, response

        except Exception as e:
            logger.error(f"UI分析处理失败: {e}")
            return True, f"UI分析失败: {str(e)}"

    def _parse_analysis_params(self, content: str) -> Optional[Dict[str, Any]]:
        """解析分析参数"""
        try:
            # 尝试从消息中提取JSON格式的参数
            import re

            # 查找JSON格式的参数
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                params = json.loads(json_match.group())
                return params

            # 如果没有JSON，检查是否有基本的分析请求
            if any(keyword in content.lower() for keyword in ["ui_analysis", "页面分析", "analyze"]):
                return {"analysis_type": "summary", "content": content}

            return None

        except Exception as e:
            logger.warning(f"解析分析参数失败: {e}")
            return None

    async def _perform_ui_analysis(self, params: Dict[str, Any]) -> PageAnalysisResult:
        """执行UI分析"""
        try:
            # 构建分析提示
            analysis_prompt = self._build_analysis_prompt(params)

            # 如果有截图，使用多模态分析
            if "screenshot_base64" in params:
                # 使用AutoGen的多模态能力
                messages = [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": analysis_prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{params['screenshot_base64']}"
                                }
                            }
                        ]
                    }
                ]
            else:
                # 纯文本分析
                messages = [{"role": "user", "content": analysis_prompt}]

            # 使用AutoGen的generate_oai_reply方法
            response = await self._generate_ui_analysis_reply(messages)

            # 解析响应为PageAnalysisResult
            analysis_result = self._parse_analysis_response(response, params)

            return analysis_result

        except Exception as e:
            logger.error(f"执行UI分析失败: {e}")
            # 返回基础分析结果
            return PageAnalysisResult(
                page_title="分析失败",
                page_type="error",
                main_content="UI分析执行失败",
                ui_elements=[],
                analysis_summary=f"分析失败: {str(e)}",
                confidence_score=0.0
            )

    async def _generate_ui_analysis_reply(self, messages: List[Dict[str, Any]]) -> str:
        """使用AutoGen生成UI分析回复"""
        try:
            # 使用AutoGen的正确方法生成回复
            # 方法1: 尝试使用generate_oai_reply (不传llm_config参数)
            try:
                response = self.generate_oai_reply(messages)
                if response:
                    logger.info("AutoGen generate_oai_reply 成功")
                    return response
            except Exception as e1:
                logger.warning(f"generate_oai_reply失败: {e1}")

            # 方法2: 尝试使用generate_reply (标准方法)
            try:
                # 构建正确的消息格式
                if isinstance(messages, list) and len(messages) > 0:
                    last_message = messages[-1]
                    if isinstance(last_message, dict) and "content" in last_message:
                        formatted_messages = [last_message]
                    else:
                        formatted_messages = [{"role": "user", "content": str(messages)}]
                else:
                    formatted_messages = [{"role": "user", "content": "请进行UI分析"}]

                response = self.generate_reply(formatted_messages)
                if response:
                    logger.info("AutoGen generate_reply 成功")
                    return response
            except Exception as e2:
                logger.warning(f"generate_reply失败: {e2}")

            # 方法3: 尝试使用a_generate_reply (异步方法)
            try:
                formatted_messages = [{"role": "user", "content": str(messages)}]
                response = await self.a_generate_reply(formatted_messages)
                if response:
                    logger.info("AutoGen a_generate_reply 成功")
                    return response
            except Exception as e3:
                logger.warning(f"a_generate_reply失败: {e3}")

            # 如果都失败，抛出异常进入降级模式
            raise Exception("所有AutoGen方法都失败")

        except Exception as e:
            logger.error(f"AutoGen生成回复失败: {e}")
            # 降级到直接API调用
            return await self._fallback_api_call(messages)

    async def _fallback_api_call(self, messages: List[Dict[str, Any]]) -> str:
        """降级API调用"""
        try:
            import httpx

            logger.info("使用降级API调用模式")

            # 构建请求
            request_data = {
                "model": self.ui_tars_model,
                "messages": messages,
                "max_tokens": 4000,
                "temperature": 0.1
            }

            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.ui_tars_base_url}/chat/completions",
                    json=request_data,
                    headers={
                        "Authorization": f"Bearer {self.ui_tars_api_key}",
                        "Content-Type": "application/json"
                    }
                )

                logger.info(f"降级API响应状态: {response.status_code}")

                if response.status_code == 200:
                    result = response.json()
                    content = result["choices"][0]["message"]["content"]
                    logger.info(f"降级API调用成功，响应长度: {len(content)}")
                    return content
                else:
                    error_text = response.text
                    logger.error(f"降级API调用失败: {response.status_code} - {error_text}")
                    raise Exception(f"API调用失败: {response.status_code}")

        except Exception as e:
            logger.error(f"降级API调用失败: {e}")
            # 返回一个基础的分析结果
            return self._create_basic_analysis_response()

    def _create_basic_analysis_response(self) -> str:
        """创建基础分析响应"""
        return """{
  "page_title": "页面分析",
  "page_type": "unknown",
  "main_content": "由于技术问题，无法完成详细的页面分析",
  "ui_elements": [],
  "layout_structure": {
    "description": "无法获取布局信息"
  },
  "analysis_summary": "UI分析服务暂时不可用，请稍后重试。建议检查网络连接和API配置。",
  "confidence_score": 0.0
}"""

    def _build_analysis_prompt(self, params: Dict[str, Any]) -> str:
        """构建分析提示"""
        analysis_type = params.get("analysis_type", "full")

        base_prompt = """请分析这个Android应用页面的UI元素。

## 分析要求
1. 识别页面中的所有重要UI元素
2. 分析每个元素的功能和交互类型
3. 确定元素的重要性评分（0-1之间）
4. 识别页面的主要内容和布局结构
5. 提供页面的整体分析摘要

## 输出格式
请严格按照以下JSON格式返回分析结果：

```json
{
  "page_title": "页面标题",
  "page_type": "页面类型",
  "main_content": "主要内容描述",
  "ui_elements": [
    {
      "element_type": "元素类型",
      "text_content": "文本内容",
      "description": "元素描述",
      "position": {"description": "位置描述"},
      "functionality": "功能描述",
      "interaction_type": "交互类型",
      "importance_score": 0.8
    }
  ],
  "layout_structure": {"description": "布局结构描述"},
  "analysis_summary": "分析摘要",
  "confidence_score": 0.9
}
```
"""

        if analysis_type == "detailed":
            base_prompt += "\n请提供详细的元素分析，包括每个元素的具体属性和测试建议。"
        elif analysis_type == "summary":
            base_prompt += "\n请提供简要的页面概览，重点关注主要功能和核心UI元素。"

        # 添加DOM信息（如果有）
        if "dom_info" in params:
            base_prompt += f"\n\nDOM结构信息：\n{params['dom_info']}"

        return base_prompt

    def _parse_analysis_response(self, response: str, params: Dict[str, Any]) -> PageAnalysisResult:
        """解析分析响应"""
        try:
            # 尝试解析JSON响应
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)

            if json_match:
                data = json.loads(json_match.group())
            else:
                # 如果没有JSON，创建基础结构
                data = {
                    "page_title": "文本分析结果",
                    "page_type": "text_response",
                    "main_content": response[:200],
                    "ui_elements": [],
                    "layout_structure": {"description": "基于文本的分析"},
                    "analysis_summary": response,
                    "confidence_score": 0.5
                }

            # 解析UI元素
            ui_elements = []
            for elem_data in data.get("ui_elements", []):
                ui_element = UIElement(
                    element_type=elem_data.get("element_type", "unknown"),
                    text_content=elem_data.get("text_content"),
                    description=elem_data.get("description"),
                    position=elem_data.get("position", {}),
                    attributes=elem_data.get("attributes", {}),
                    functionality=elem_data.get("functionality"),
                    interaction_type=elem_data.get("interaction_type"),
                    importance_score=float(elem_data.get("importance_score", 0.0))
                )
                ui_elements.append(ui_element)

            # 构建分析结果
            analysis_result = PageAnalysisResult(
                page_title=data.get("page_title"),
                page_type=data.get("page_type"),
                main_content=data.get("main_content"),
                ui_elements=ui_elements,
                layout_structure=data.get("layout_structure", {}),
                analysis_summary=data.get("analysis_summary", ""),
                confidence_score=float(data.get("confidence_score", 0.0))
            )

            return analysis_result

        except Exception as e:
            logger.error(f"解析分析响应失败: {e}")
            return PageAnalysisResult(
                page_title="解析失败",
                page_type="error",
                main_content="响应解析失败",
                ui_elements=[],
                analysis_summary=f"解析失败: {str(e)}",
                confidence_score=0.0
            )

    def _format_analysis_response(self, analysis_result: PageAnalysisResult) -> str:
        """格式化分析响应"""
        return f"""🎯 Android UI页面分析完成

📄 页面信息：
- 标题：{analysis_result.page_title or '未识别'}
- 类型：{analysis_result.page_type or '未知'}
- 主要内容：{analysis_result.main_content or '无描述'}

🔍 UI元素统计：
- 总元素数：{len(analysis_result.ui_elements)}
- 置信度：{analysis_result.confidence_score:.2f}

📝 分析摘要：
{analysis_result.analysis_summary}

详细的结构化数据已保存到分析历史中。"""

    def _record_analysis(self, params: Dict[str, Any], result: PageAnalysisResult):
        """记录分析历史"""
        try:
            record = {
                "timestamp": datetime.now().isoformat(),
                "analysis_type": params.get("analysis_type", "unknown"),
                "ui_elements_count": len(result.ui_elements),
                "confidence_score": result.confidence_score,
                "page_title": result.page_title,
                "page_type": result.page_type
            }

            self.analysis_history.append(record)

            # 保持历史记录数量限制
            if len(self.analysis_history) > 100:
                self.analysis_history = self.analysis_history[-100:]

        except Exception as e:
            logger.warning(f"记录分析历史失败: {e}")

    async def analyze_page_with_autogen(
        self,
        dom_info: Optional[PageDOMInfo] = None,
        screenshot_info: Optional[ScreenshotInfo] = None,
        analysis_type: str = "full",
        custom_prompt: Optional[str] = None
    ) -> PageAnalysisResult:
        """
        使用AutoGen框架进行页面分析的公共接口

        Args:
            dom_info: DOM信息
            screenshot_info: 截图信息
            analysis_type: 分析类型
            custom_prompt: 自定义提示

        Returns:
            PageAnalysisResult: 分析结果
        """
        try:
            # 构建分析参数
            params = {
                "analysis_type": analysis_type,
                "custom_prompt": custom_prompt
            }

            if screenshot_info and screenshot_info.image_base64:
                params["screenshot_base64"] = screenshot_info.image_base64
            elif screenshot_info and screenshot_info.image_path:
                # 读取图片文件并转换为base64
                with open(screenshot_info.image_path, "rb") as img_file:
                    import base64
                    params["screenshot_base64"] = base64.b64encode(img_file.read()).decode('utf-8')

            if dom_info:
                params["dom_info"] = f"DOM元素数量: {dom_info.total_elements}"

            # 执行分析
            analysis_result = await self._perform_ui_analysis(params)

            logger.info(f"AutoGen UI分析完成: {analysis_result.page_title}")
            return analysis_result

        except Exception as e:
            logger.error(f"AutoGen页面分析失败: {e}")
            return PageAnalysisResult(
                page_title="分析失败",
                page_type="error",
                main_content="AutoGen分析失败",
                ui_elements=[],
                analysis_summary=f"分析失败: {str(e)}",
                confidence_score=0.0
            )

    def get_analysis_history(self) -> List[Dict[str, Any]]:
        """获取分析历史"""
        return self.analysis_history.copy()

    def get_agent_status(self) -> Dict[str, Any]:
        """获取智能体状态"""
        return {
            "name": self.name,
            "model": self.ui_tars_model,
            "analysis_count": len(self.analysis_history),
            "llm_config": self.llm_config,
            "system_message_length": len(self.system_message) if self.system_message else 0
        }


# 工厂函数和全局实例管理
def create_ui_tars_agent(
    name: str = "ui_tars_analyzer",
    system_message: Optional[str] = None,
    llm_config: Optional[Dict[str, Any]] = None,
    **kwargs
) -> UITarsAnalysisAgent:
    """
    创建UI_TARS分析智能体的工厂函数

    Args:
        name: 智能体名称
        system_message: 系统消息
        llm_config: LLM配置
        **kwargs: 其他参数

    Returns:
        UITarsAnalysisAgent: 配置好的智能体实例
    """
    agent = UITarsAnalysisAgent(
        name=name,
        system_message=system_message,
        llm_config=llm_config,
        **kwargs
    )

    logger.info(f"创建UI_TARS分析智能体: {name}")
    return agent


# 全局UI_TARS分析智能体实例
ui_tars_service = create_ui_tars_agent()

# 为了向后兼容，保留原有的别名
ui_tars_agent = ui_tars_service


def get_ui_tars_agent() -> UITarsAnalysisAgent:
    """获取全局UI_TARS智能体实例"""
    return ui_tars_service


def reset_ui_tars_agent():
    """重置全局UI_TARS智能体"""
    global ui_tars_service
    ui_tars_service = create_ui_tars_agent()
    logger.info("全局UI_TARS智能体已重置")
