"""
聊天相关的数据模型
"""
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class MessageRole(str, Enum):
    """消息角色枚举"""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class ChatMessage(BaseModel):
    """聊天消息模型"""
    role: MessageRole
    content: str
    timestamp: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None


class ChatRequest(BaseModel):
    """聊天请求模型"""
    message: str = Field(..., description="用户消息")
    conversation_id: Optional[str] = Field(None, description="会话ID")
    stream: bool = Field(True, description="是否流式响应")
    max_tokens: Optional[int] = Field(None, description="最大token数")
    temperature: Optional[float] = Field(0.7, description="温度参数")


class ChatResponse(BaseModel):
    """聊天响应模型"""
    message: str
    conversation_id: str
    timestamp: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None


class StreamChunk(BaseModel):
    """流式响应块模型"""
    content: str
    conversation_id: str
    is_complete: bool = False
    metadata: Optional[Dict[str, Any]] = None
