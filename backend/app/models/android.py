"""
Android页面分析相关的数据模型
"""
from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class AndroidDeviceInfo(BaseModel):
    """Android设备信息"""
    device_id: str = Field(..., description="设备ID")
    device_name: Optional[str] = Field(None, description="设备名称")
    platform_version: Optional[str] = Field(None, description="Android版本")
    screen_size: Optional[str] = Field(None, description="屏幕尺寸")
    is_connected: bool = Field(False, description="是否已连接")


class AppLaunchRequest(BaseModel):
    """启动APP请求"""
    package_name: str = Field(..., description="应用包名")
    activity_name: Optional[str] = Field(None, description="Activity名称")
    device_id: Optional[str] = Field(None, description="设备ID")
    wait_timeout: int = Field(10, description="等待超时时间(秒)")


class NavigationRequest(BaseModel):
    """页面导航请求"""
    actions: List[Dict[str, Any]] = Field(..., description="导航操作列表")
    device_id: Optional[str] = Field(None, description="设备ID")


class ElementInfo(BaseModel):
    """页面元素信息"""
    resource_id: Optional[str] = Field(None, description="资源ID")
    class_name: Optional[str] = Field(None, description="类名")
    text: Optional[str] = Field(None, description="文本内容")
    content_desc: Optional[str] = Field(None, description="内容描述")
    bounds: Optional[Dict[str, int]] = Field(None, description="元素边界")
    clickable: bool = Field(False, description="是否可点击")
    enabled: bool = Field(True, description="是否启用")
    focused: bool = Field(False, description="是否聚焦")
    selected: bool = Field(False, description="是否选中")
    checkable: bool = Field(False, description="是否可选择")
    checked: bool = Field(False, description="是否已选择")
    scrollable: bool = Field(False, description="是否可滚动")
    long_clickable: bool = Field(False, description="是否可长按")
    password: bool = Field(False, description="是否为密码字段")
    xpath: Optional[str] = Field(None, description="XPath路径")
    children: List['ElementInfo'] = Field(default_factory=list, description="子元素")


class PageDOMInfo(BaseModel):
    """页面DOM信息"""
    xml_source: str = Field(..., description="页面XML源码")
    elements: List[ElementInfo] = Field(..., description="页面元素列表")
    total_elements: int = Field(..., description="元素总数")
    timestamp: datetime = Field(default_factory=datetime.now, description="获取时间")


class ScreenshotInfo(BaseModel):
    """截图信息"""
    image_path: str = Field(..., description="截图文件路径")
    image_base64: Optional[str] = Field(None, description="截图Base64编码")
    width: int = Field(..., description="图片宽度")
    height: int = Field(..., description="图片高度")
    timestamp: datetime = Field(default_factory=datetime.now, description="截图时间")


class UIAnalysisRequest(BaseModel):
    """UI分析请求"""
    dom_info: PageDOMInfo = Field(..., description="DOM信息")
    screenshot_info: ScreenshotInfo = Field(..., description="截图信息")
    analysis_type: str = Field("full", description="分析类型")
    custom_prompt: Optional[str] = Field(None, description="自定义提示")


class UIElement(BaseModel):
    """UI元素分析结果"""
    element_type: str = Field(..., description="元素类型")
    text_content: Optional[str] = Field(None, description="文本内容")
    description: Optional[str] = Field(None, description="元素描述")
    position: Dict[str, Any] = Field(default_factory=dict, description="位置信息")
    attributes: Dict[str, Any] = Field(default_factory=dict, description="元素属性")
    functionality: Optional[str] = Field(None, description="功能描述")
    interaction_type: Optional[str] = Field(None, description="交互类型")
    importance_score: float = Field(0.0, description="重要性评分")


class PageAnalysisResult(BaseModel):
    """页面分析结果"""
    page_title: Optional[str] = Field(None, description="页面标题")
    page_type: Optional[str] = Field(None, description="页面类型")
    main_content: Optional[str] = Field(None, description="主要内容")
    ui_elements: List[UIElement] = Field(..., description="UI元素列表")
    navigation_elements: List[UIElement] = Field(default_factory=list, description="导航元素")
    interactive_elements: List[UIElement] = Field(default_factory=list, description="交互元素")
    text_elements: List[UIElement] = Field(default_factory=list, description="文本元素")
    layout_structure: Dict[str, Any] = Field(default_factory=dict, description="布局结构")
    accessibility_info: Dict[str, Any] = Field(default_factory=dict, description="无障碍信息")
    analysis_summary: str = Field(..., description="分析摘要")
    confidence_score: float = Field(0.0, description="分析置信度")
    timestamp: datetime = Field(default_factory=datetime.now, description="分析时间")


class AndroidPageAnalysisRequest(BaseModel):
    """Android页面分析完整请求"""
    app_launch: AppLaunchRequest = Field(..., description="应用启动配置")
    navigation: Optional[NavigationRequest] = Field(None, description="导航配置")
    analysis_config: Dict[str, Any] = Field(default_factory=dict, description="分析配置")
    output_format: str = Field("json", description="输出格式")


class AndroidPageAnalysisResponse(BaseModel):
    """Android页面分析完整响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    device_info: Optional[AndroidDeviceInfo] = Field(None, description="设备信息")
    app_info: Dict[str, Any] = Field(default_factory=dict, description="应用信息")
    dom_info: Optional[PageDOMInfo] = Field(None, description="DOM信息")
    screenshot_info: Optional[ScreenshotInfo] = Field(None, description="截图信息")
    analysis_result: Optional[PageAnalysisResult] = Field(None, description="分析结果")
    execution_time: float = Field(0.0, description="执行时间(秒)")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")


# 更新前向引用
ElementInfo.model_rebuild()
