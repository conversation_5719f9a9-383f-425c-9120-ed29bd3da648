"""
Server-Sent Events (SSE) 工具模块
"""
import json
import asyncio
from typing import AsyncGenerator, Dict, Any, Optional
from fastapi import Response
from fastapi.responses import StreamingResponse
from loguru import logger


class SSEResponse:
    """SSE响应工具类"""
    
    @staticmethod
    def format_sse_data(data: Dict[str, Any], event: Optional[str] = None) -> str:
        """
        格式化SSE数据
        
        Args:
            data: 要发送的数据
            event: 事件类型
            
        Returns:
            格式化后的SSE字符串
        """
        lines = []
        
        if event:
            lines.append(f"event: {event}")
        
        # 将数据转换为JSON字符串
        json_data = json.dumps(data, ensure_ascii=False)
        lines.append(f"data: {json_data}")
        lines.append("")  # 空行表示消息结束
        
        return "\n".join(lines)
    
    @staticmethod
    def create_stream_response(
        generator: AsyncGenerator[str, None],
        headers: Optional[Dict[str, str]] = None
    ) -> StreamingResponse:
        """
        创建流式响应
        
        Args:
            generator: 异步生成器
            headers: 额外的响应头
            
        Returns:
            StreamingResponse对象
        """
        default_headers = {
            "Content-Type": "text/event-stream",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control",
            "Content-Encoding": "identity",  # 禁用压缩
        }
        
        if headers:
            default_headers.update(headers)
        
        return StreamingResponse(
            generator,
            media_type="text/event-stream",
            headers=default_headers
        )


class SSEManager:
    """SSE连接管理器"""
    
    def __init__(self):
        self.connections: Dict[str, asyncio.Queue] = {}
    
    async def add_connection(self, connection_id: str) -> asyncio.Queue:
        """添加连接"""
        queue = asyncio.Queue()
        self.connections[connection_id] = queue
        logger.info(f"SSE连接已添加: {connection_id}")
        return queue
    
    async def remove_connection(self, connection_id: str):
        """移除连接"""
        if connection_id in self.connections:
            del self.connections[connection_id]
            logger.info(f"SSE连接已移除: {connection_id}")
    
    async def send_to_connection(
        self, 
        connection_id: str, 
        data: Dict[str, Any], 
        event: Optional[str] = None
    ):
        """向指定连接发送数据"""
        if connection_id in self.connections:
            sse_data = SSEResponse.format_sse_data(data, event)
            await self.connections[connection_id].put(sse_data)
    
    async def broadcast(self, data: Dict[str, Any], event: Optional[str] = None):
        """广播数据到所有连接"""
        sse_data = SSEResponse.format_sse_data(data, event)
        for queue in self.connections.values():
            await queue.put(sse_data)
    
    async def stream_generator(self, connection_id: str) -> AsyncGenerator[str, None]:
        """生成SSE流数据"""
        queue = await self.add_connection(connection_id)
        
        try:
            while True:
                try:
                    # 等待数据，设置超时避免无限等待
                    data = await asyncio.wait_for(queue.get(), timeout=30.0)
                    yield data
                except asyncio.TimeoutError:
                    # 发送心跳包保持连接
                    heartbeat = SSEResponse.format_sse_data(
                        {"type": "heartbeat", "timestamp": asyncio.get_event_loop().time()},
                        "heartbeat"
                    )
                    yield heartbeat
                except Exception as e:
                    logger.error(f"SSE流生成错误: {e}")
                    break
        finally:
            await self.remove_connection(connection_id)


# 全局SSE管理器实例
sse_manager = SSEManager()
