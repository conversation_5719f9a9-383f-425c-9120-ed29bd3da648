2025-08-26 17:20:08.732 | INFO     | app.main:lifespan:24 - 正在启动UI自动化测试系统...
2025-08-26 17:20:08.734 | INFO     | app.services.autogen_service:initialize:208 - AutoGen运行时初始化成功
2025-08-26 17:20:08.734 | INFO     | app.main:lifespan:33 - AutoGen服务初始化成功
2025-08-26 17:20:08.734 | INFO     | app.main:lifespan:37 - 系统启动完成
2025-08-26 17:25:10.619 | INFO     | app.main:lifespan:42 - 正在关闭系统...
2025-08-26 17:25:10.623 | ERROR    | app.main:lifespan:50 - 关闭AutoGen服务时出错: Runtime is not started
2025-08-26 17:25:10.624 | INFO     | app.main:lifespan:52 - 系统已关闭
2025-08-26 17:25:12.722 | INFO     | app.main:lifespan:24 - 正在启动UI自动化测试系统...
2025-08-26 17:25:12.724 | INFO     | app.services.autogen_service:initialize:208 - AutoGen运行时初始化成功
2025-08-26 17:25:12.724 | INFO     | app.main:lifespan:33 - AutoGen服务初始化成功
2025-08-26 17:25:12.724 | INFO     | app.main:lifespan:37 - 系统启动完成
2025-08-26 17:27:04.207 | INFO     | app.services.android_device_service:get_connected_devices:49 - 发现 1 个Android设备
2025-08-26 17:27:04.821 | INFO     | app.services.android_device_service:get_connected_devices:49 - 发现 1 个Android设备
2025-08-26 17:35:01.072 | INFO     | app.main:lifespan:42 - 正在关闭系统...
2025-08-26 17:35:01.077 | ERROR    | app.main:lifespan:50 - 关闭AutoGen服务时出错: Runtime is not started
2025-08-26 17:35:01.077 | INFO     | app.main:lifespan:52 - 系统已关闭
2025-08-26 17:35:13.124 | INFO     | app.main:lifespan:24 - 正在启动UI自动化测试系统...
2025-08-26 17:35:13.126 | INFO     | app.services.autogen_service:initialize:208 - AutoGen运行时初始化成功
2025-08-26 17:35:13.127 | INFO     | app.main:lifespan:33 - AutoGen服务初始化成功
2025-08-26 17:35:13.127 | INFO     | app.main:lifespan:37 - 系统启动完成
2025-08-26 17:35:42.526 | INFO     | app.services.android_analysis_service:analyze_current_page:187 - 开始分析当前页面
2025-08-26 17:35:42.788 | INFO     | app.services.android_device_service:get_connected_devices:49 - 发现 1 个Android设备
2025-08-26 17:36:09.447 | INFO     | app.services.android_device_service:get_connected_devices:49 - 发现 1 个Android设备
2025-08-26 17:36:26.151 | INFO     | app.services.android_device_service:get_connected_devices:49 - 发现 1 个Android设备
2025-08-26 17:36:26.350 | INFO     | app.services.page_capture_service:capture_page_dom:47 - 成功捕获页面DOM，共 1 个元素
2025-08-26 17:36:26.552 | INFO     | app.services.android_device_service:get_connected_devices:49 - 发现 1 个Android设备
2025-08-26 17:36:26.881 | INFO     | app.services.page_capture_service:capture_screenshot:154 - 成功捕获截图: static/screenshots\screenshot_20250826_173626_552.png (1080x2400)
2025-08-26 17:36:26.883 | INFO     | app.services.page_capture_service:capture_page_info:172 - 成功捕获页面DOM和截图
2025-08-26 17:43:15.247 | INFO     | app.services.android_device_service:get_connected_devices:49 - 发现 1 个Android设备
2025-08-26 17:43:15.400 | INFO     | app.services.page_capture_service:capture_page_dom:47 - 成功捕获页面DOM，共 1 个元素
2025-08-26 17:43:15.567 | INFO     | app.services.android_device_service:get_connected_devices:49 - 发现 1 个Android设备
2025-08-26 17:43:15.760 | INFO     | app.services.page_capture_service:capture_screenshot:154 - 成功捕获截图: static/screenshots\screenshot_20250826_174315_568.png (1080x2400)
2025-08-26 17:43:15.760 | INFO     | app.services.page_capture_service:capture_page_info:172 - 成功捕获页面DOM和截图
2025-08-26 17:59:03.006 | ERROR    | app.services.ui_tars_service:_parse_analysis_result:244 - 解析分析结果失败: 1 validation error for UIElement
position
  Input should be a valid dictionary [type=dict_type, input_value='顶部导航栏左侧', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
2025-08-26 17:59:03.009 | INFO     | app.services.ui_tars_service:analyze_page:49 - UI_TARS分析完成，识别到 0 个UI元素
2025-08-26 18:00:47.457 | INFO     | app.services.android_analysis_service:analyze_current_page:220 - 当前页面分析完成，耗时 1400.48 秒
2025-08-26 18:01:11.062 | INFO     | app.main:lifespan:42 - 正在关闭系统...
2025-08-26 18:01:11.063 | ERROR    | app.main:lifespan:50 - 关闭AutoGen服务时出错: Runtime is not started
2025-08-26 18:01:11.064 | INFO     | app.main:lifespan:52 - 系统已关闭
2025-08-26 18:10:48.860 | INFO     | app.main:lifespan:24 - 正在启动UI自动化测试系统...
2025-08-26 18:10:48.862 | INFO     | app.services.autogen_service:initialize:208 - AutoGen运行时初始化成功
2025-08-26 18:10:48.862 | INFO     | app.main:lifespan:33 - AutoGen服务初始化成功
2025-08-26 18:10:48.862 | INFO     | app.main:lifespan:37 - 系统启动完成
2025-08-26 18:13:34.775 | INFO     | app.main:lifespan:42 - 正在关闭系统...
2025-08-26 18:13:34.775 | ERROR    | app.main:lifespan:50 - 关闭AutoGen服务时出错: Runtime is not started
2025-08-26 18:13:34.776 | INFO     | app.main:lifespan:52 - 系统已关闭
2025-08-26 18:13:42.633 | INFO     | app.main:lifespan:24 - 正在启动UI自动化测试系统...
2025-08-26 18:13:42.634 | INFO     | app.services.autogen_service:initialize:208 - AutoGen运行时初始化成功
2025-08-26 18:13:42.635 | INFO     | app.main:lifespan:33 - AutoGen服务初始化成功
2025-08-26 18:13:42.635 | INFO     | app.main:lifespan:37 - 系统启动完成
2025-08-26 18:13:59.225 | INFO     | app.services.android_analysis_service:analyze_current_page:211 - 开始分析当前页面
2025-08-26 18:14:00.713 | INFO     | app.services.android_device_service:get_connected_devices:49 - 发现 1 个Android设备
2025-08-26 18:14:00.907 | INFO     | app.services.android_device_service:get_connected_devices:49 - 发现 1 个Android设备
2025-08-26 18:14:01.461 | INFO     | app.services.android_device_service:get_connected_devices:49 - 发现 1 个Android设备
2025-08-26 18:14:01.772 | INFO     | app.services.page_capture_service:capture_page_dom:40 - 使用标准方法成功获取DOM
2025-08-26 18:14:01.775 | INFO     | app.services.page_capture_service:capture_page_dom:73 - 成功捕获页面DOM，共 1 个元素
2025-08-26 18:14:01.993 | INFO     | app.services.android_device_service:get_connected_devices:49 - 发现 1 个Android设备
2025-08-26 18:14:02.452 | INFO     | app.services.page_capture_service:capture_screenshot:180 - 成功捕获截图: static/screenshots\screenshot_20250826_181401_993.png (1080x2400)
2025-08-26 18:14:02.452 | INFO     | app.services.page_capture_service:capture_page_info:198 - 成功捕获页面DOM和截图
2025-08-26 18:17:10.341 | INFO     | app.services.ui_tars_service:_parse_analysis_result:217 - 开始解析 5 个UI元素
2025-08-26 18:17:10.343 | ERROR    | app.services.ui_tars_service:_parse_analysis_result:279 - 解析分析结果失败: 1 validation error for PageAnalysisResult
layout_structure
  Input should be a valid dictionary [type=dict_type, input_value='顶部状态栏，中间...d 主屏幕分层布局', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
2025-08-26 18:17:10.343 | INFO     | app.services.ui_tars_service:analyze_page:53 - UI_TARS分析完成，识别到 0 个UI元素
2025-08-26 18:17:10.344 | INFO     | app.services.android_analysis_service:analyze_current_page:244 - 当前页面分析完成，耗时 191.12 秒
2025-08-26 18:19:34.712 | INFO     | app.services.android_analysis_service:analyze_current_page:211 - 开始分析当前页面
2025-08-26 18:19:34.930 | INFO     | app.services.android_device_service:get_connected_devices:49 - 发现 1 个Android设备
2025-08-26 18:19:35.153 | INFO     | app.services.android_device_service:get_connected_devices:49 - 发现 1 个Android设备
2025-08-26 18:19:35.712 | INFO     | app.services.android_device_service:get_connected_devices:49 - 发现 1 个Android设备
2025-08-26 18:19:36.029 | INFO     | app.services.page_capture_service:capture_page_dom:40 - 使用标准方法成功获取DOM
2025-08-26 18:19:36.031 | INFO     | app.services.page_capture_service:capture_page_dom:73 - 成功捕获页面DOM，共 1 个元素
2025-08-26 18:19:36.214 | INFO     | app.services.android_device_service:get_connected_devices:49 - 发现 1 个Android设备
2025-08-26 18:19:36.482 | INFO     | app.services.page_capture_service:capture_screenshot:180 - 成功捕获截图: static/screenshots\screenshot_20250826_181936_214.png (1080x2400)
2025-08-26 18:19:36.483 | INFO     | app.services.page_capture_service:capture_page_info:198 - 成功捕获页面DOM和截图
2025-08-26 18:19:58.463 | INFO     | app.services.ui_tars_service:_parse_analysis_result:217 - 开始解析 4 个UI元素
2025-08-26 18:19:58.464 | ERROR    | app.services.ui_tars_service:_parse_analysis_result:279 - 解析分析结果失败: 1 validation error for PageAnalysisResult
layout_structure
  Input should be a valid dictionary [type=dict_type, input_value='顶部状态栏（显示...d 主屏幕分层布局', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
2025-08-26 18:19:58.464 | INFO     | app.services.ui_tars_service:analyze_page:53 - UI_TARS分析完成，识别到 0 个UI元素
2025-08-26 18:19:58.464 | INFO     | app.services.android_analysis_service:analyze_current_page:244 - 当前页面分析完成，耗时 23.75 秒
2025-08-26 18:57:19.093 | INFO     | app.main:lifespan:42 - 正在关闭系统...
2025-08-26 18:57:19.103 | ERROR    | app.main:lifespan:50 - 关闭AutoGen服务时出错: Runtime is not started
2025-08-26 18:57:19.104 | INFO     | app.main:lifespan:52 - 系统已关闭
