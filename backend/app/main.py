"""
FastAPI主应用
"""
import os
import asyncio
from contextlib import asynccontextmanager
from pathlib import Path

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.staticfiles import StaticFiles
from loguru import logger

from app.core.config import settings
from app.api.api import api_router
from app.services.autogen_service import autogen_service


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("正在启动UI自动化测试系统...")
    
    # 创建必要的目录
    os.makedirs("logs", exist_ok=True)
    os.makedirs("static", exist_ok=True)
    
    # 初始化AutoGen服务
    try:
        await autogen_service.initialize()
        logger.info("AutoGen服务初始化成功")
    except Exception as e:
        logger.error(f"AutoGen服务初始化失败: {e}")
    
    logger.info("系统启动完成")
    
    yield
    
    # 关闭时执行
    logger.info("正在关闭系统...")
    
    # 清理AutoGen服务
    if autogen_service.runtime:
        try:
            await autogen_service.runtime.stop()
            logger.info("AutoGen服务已关闭")
        except Exception as e:
            logger.error(f"关闭AutoGen服务时出错: {e}")
    
    logger.info("系统已关闭")


# 创建FastAPI应用
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="基于FastAPI和AutoGen的UI自动化测试系统",
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url=f"{settings.API_V1_STR}/docs",
    redoc_url=f"{settings.API_V1_STR}/redoc",
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加Gzip压缩中间件
app.add_middleware(GZipMiddleware, minimum_size=1000)

# 包含API路由
app.include_router(api_router, prefix=settings.API_V1_STR)

# 静态文件服务
static_dir = Path("static")
static_dir.mkdir(exist_ok=True)
app.mount("/static", StaticFiles(directory="static"), name="static")


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "docs": f"{settings.API_V1_STR}/docs",
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "app_name": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "autogen_status": "initialized" if autogen_service.runtime else "not_initialized"
    }


if __name__ == "__main__":
    import uvicorn
    
    # 配置日志
    logger.add(
        settings.LOG_FILE,
        rotation="1 day",
        retention="30 days",
        level=settings.LOG_LEVEL
    )
    
    # 启动服务器
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
