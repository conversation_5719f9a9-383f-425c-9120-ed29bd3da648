# UI自动化测试系统环境配置文件
# 请根据实际环境修改以下配置

# =============================================================================
# 应用基础配置
# =============================================================================
APP_NAME=UI自动化测试系统
APP_VERSION=1.0.0
DEBUG=false
HOST=0.0.0.0
PORT=8000
API_V1_STR=/api/v1

# =============================================================================
# OpenAI模型配置 (通用GPT模型)
# =============================================================================
OPENAI_API_KEY=sk_73a4950b1fe27d5d73b61b33a88aa2870474b9d81c857d6331e60a0
OPENAI_API_BASE=https://hk-intra-paas.transsion.com/tranai-proxy/v1
OPENAI_MODEL=gpt-4o

# =============================================================================
# DeepSeek模型配置 (代码生成和分析)
# =============================================================================
DEEPSEEK_API_KEY=sk_73a4950b1fe27d5d73b61b33a88aa2870474b9d81c857d6331e60a0
DEEPSEEK_BASE_URL=https://hk-intra-paas.transsion.com/tranai-proxy/v1
DEEPSEEK_MODEL=deepseek-chat

# =============================================================================
# Qwen-VL模型配置 (视觉理解)
# =============================================================================
QWEN_VL_API_KEY=sk_73a4950b1fe27d5d73b61b33a88aa2870474b9d81c857d6331e60a0
QWEN_VL_BASE_URL=https://hk-intra-paas.transsion.com/tranai-proxy/v1
QWEN_VL_MODEL=qwen-vl-max-latest

# =============================================================================
# UI_TARS模型配置 (UI界面分析专用)
# =============================================================================
UI_TARS_API_KEY=7cd14776-e901-4875-868d-e01ee77a4eb2
UI_TARS_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
UI_TARS_MODEL=doubao-1-5-ui-tars-250428

# =============================================================================
# AI模型通用配置
# =============================================================================
DEFAULT_TEMPERATURE=0.1
DEFAULT_MAX_TOKENS=4000
DEFAULT_TIMEOUT=120

# 模型选择配置
DEFAULT_CHAT_MODEL=openai
DEFAULT_VISION_MODEL=qwen_vl
DEFAULT_CODE_MODEL=deepseek

# =============================================================================
# AutoGen配置
# =============================================================================
AUTOGEN_CACHE_ENABLED=true
AUTOGEN_MAX_ROUND=10
AUTOGEN_TIMEOUT=600
AUTOGEN_CACHE_DIR=.cache/autogen
AUTOGEN_WORK_DIR=workspace

# =============================================================================
# Android自动化配置
# =============================================================================
APPIUM_SERVER_URL=http://localhost:4723
ANDROID_DEVICE_NAME=
ANDROID_PLATFORM_VERSION=
ADB_PATH=
ADB_TIMEOUT=30
UIAUTOMATOR2_TIMEOUT=60
UIAUTOMATOR2_RETRY_COUNT=3

# =============================================================================
# 缓存配置
# =============================================================================
REDIS_URL=
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# =============================================================================
# 文件存储配置
# =============================================================================
UPLOAD_DIR=uploads
STATIC_DIR=static
SCREENSHOT_DIR=static/screenshots
TEMP_DIR=temp

# =============================================================================
# 性能监控配置
# =============================================================================
ENABLE_METRICS=true
METRICS_RETENTION_DAYS=7
MAX_CONCURRENT_ANALYSIS=5

# =============================================================================
# 安全配置
# =============================================================================
SECRET_KEY=your-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# =============================================================================
# 数据库配置 (可选)
# =============================================================================
DATABASE_URL=

# =============================================================================
# 日志配置
# =============================================================================
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
LOG_ROTATION=1 day
LOG_RETENTION=30 days
LOG_FORMAT={time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}
