"""
应用配置模块
"""
import os
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置"""
    
    # 应用基础配置
    APP_NAME: str = Field(default="UI自动化测试系统", env="APP_NAME")
    APP_VERSION: str = Field(default="1.0.0", env="APP_VERSION")
    DEBUG: bool = Field(default=False, env="DEBUG")

    # API配置
    API_V1_STR: str = Field(default="/api/v1", env="API_V1_STR")
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8000, env="PORT")
    
    # CORS配置
    BACKEND_CORS_ORIGINS: list = [
        "http://localhost:3000",
        "http://localhost:8080",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:8080",
    ]
    
    # AutoGen配置
    AUTOGEN_CACHE_ENABLED: bool = True
    AUTOGEN_MAX_ROUND: int = 10
    AUTOGEN_TIMEOUT: int = 600  # 10分钟
    AUTOGEN_CACHE_DIR: str = ".cache/autogen"  # AutoGen缓存目录
    AUTOGEN_WORK_DIR: str = "workspace"  # AutoGen工作目录

    # AI模型通用配置
    DEFAULT_TEMPERATURE: float = Field(default=0.1, env="DEFAULT_TEMPERATURE")
    DEFAULT_MAX_TOKENS: int = Field(default=4000, env="DEFAULT_MAX_TOKENS")
    DEFAULT_TIMEOUT: int = Field(default=120, env="DEFAULT_TIMEOUT")

    # 模型选择配置
    DEFAULT_CHAT_MODEL: str = Field(default="openai", env="DEFAULT_CHAT_MODEL")
    DEFAULT_VISION_MODEL: str = Field(default="qwen_vl", env="DEFAULT_VISION_MODEL")
    DEFAULT_CODE_MODEL: str = Field(default="deepseek", env="DEFAULT_CODE_MODEL")
    
    # OpenAI模型配置 (通用GPT模型)
    OPENAI_API_KEY: str = Field(env="OPENAI_API_KEY")
    OPENAI_API_BASE: str = Field(env="OPENAI_API_BASE")
    OPENAI_MODEL: str = Field(default="gpt-4o", env="OPENAI_MODEL")

    # DeepSeek模型配置 (代码生成和分析)
    DEEPSEEK_API_KEY: str = Field(env="DEEPSEEK_API_KEY")
    DEEPSEEK_BASE_URL: str = Field(env="DEEPSEEK_BASE_URL")
    DEEPSEEK_MODEL: str = Field(default="deepseek-chat", env="DEEPSEEK_MODEL")

    # Qwen-VL模型配置 (视觉理解)
    QWEN_VL_API_KEY: str = Field(env="QWEN_VL_API_KEY")
    QWEN_VL_BASE_URL: str = Field(env="QWEN_VL_BASE_URL")
    QWEN_VL_MODEL: str = Field(default="qwen-vl-max-latest", env="QWEN_VL_MODEL")

    # UI_TARS模型配置 (UI界面分析专用)
    UI_TARS_API_KEY: str = Field(env="UI_TARS_API_KEY")
    UI_TARS_BASE_URL: str = Field(env="UI_TARS_BASE_URL")
    UI_TARS_MODEL: str = Field(default="doubao-1-5-ui-tars-250428", env="UI_TARS_MODEL")

    # Android自动化配置
    APPIUM_SERVER_URL: str = Field(default="http://localhost:4723", env="APPIUM_SERVER_URL")
    ANDROID_DEVICE_NAME: Optional[str] = Field(default=None, env="ANDROID_DEVICE_NAME")
    ANDROID_PLATFORM_VERSION: Optional[str] = Field(default=None, env="ANDROID_PLATFORM_VERSION")

    # ADB配置
    ADB_PATH: Optional[str] = Field(default=None, env="ADB_PATH")
    ADB_TIMEOUT: int = Field(default=30, env="ADB_TIMEOUT")

    # UIAutomator2配置
    UIAUTOMATOR2_TIMEOUT: int = Field(default=60, env="UIAUTOMATOR2_TIMEOUT")
    UIAUTOMATOR2_RETRY_COUNT: int = Field(default=3, env="UIAUTOMATOR2_RETRY_COUNT")

    # 缓存配置
    REDIS_URL: Optional[str] = Field(default=None, env="REDIS_URL")
    CACHE_TTL: int = Field(default=3600, env="CACHE_TTL")
    CACHE_MAX_SIZE: int = Field(default=1000, env="CACHE_MAX_SIZE")

    # 文件存储配置
    UPLOAD_DIR: str = Field(default="uploads", env="UPLOAD_DIR")
    STATIC_DIR: str = Field(default="static", env="STATIC_DIR")
    SCREENSHOT_DIR: str = Field(default="static/screenshots", env="SCREENSHOT_DIR")
    TEMP_DIR: str = Field(default="temp", env="TEMP_DIR")

    # 性能监控配置
    ENABLE_METRICS: bool = Field(default=True, env="ENABLE_METRICS")
    METRICS_RETENTION_DAYS: int = Field(default=7, env="METRICS_RETENTION_DAYS")
    MAX_CONCURRENT_ANALYSIS: int = Field(default=5, env="MAX_CONCURRENT_ANALYSIS")

    # 安全配置
    SECRET_KEY: str = Field(default="your-secret-key-change-in-production", env="SECRET_KEY")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    ALGORITHM: str = Field(default="HS256", env="ALGORITHM")

    # 数据库配置 (可选)
    DATABASE_URL: Optional[str] = Field(default=None, env="DATABASE_URL")

    # 日志配置
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FILE: str = Field(default="logs/app.log", env="LOG_FILE")
    LOG_ROTATION: str = Field(default="1 day", env="LOG_ROTATION")
    LOG_RETENTION: str = Field(default="30 days", env="LOG_RETENTION")
    LOG_FORMAT: str = Field(default="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}", env="LOG_FORMAT")
    
    def validate_ai_models(self) -> bool:
        """验证AI模型配置"""
        required_models = {
            "OpenAI": self.OPENAI_API_KEY,
            "DeepSeek": self.DEEPSEEK_API_KEY,
            "Qwen-VL": self.QWEN_VL_API_KEY,
            "UI-TARS": self.UI_TARS_API_KEY
        }

        configured_models = [name for name, key in required_models.items() if key]

        if not configured_models:
            raise ValueError("至少需要配置一个AI模型的API密钥")

        return True

    def get_model_config_summary(self) -> dict:
        """获取模型配置摘要"""
        return {
            "openai_configured": bool(self.OPENAI_API_KEY),
            "deepseek_configured": bool(self.DEEPSEEK_API_KEY),
            "qwen_vl_configured": bool(self.QWEN_VL_API_KEY),
            "ui_tars_configured": bool(self.UI_TARS_API_KEY),
            "default_chat_model": self.DEFAULT_CHAT_MODEL,
            "default_vision_model": self.DEFAULT_VISION_MODEL,
            "default_code_model": self.DEFAULT_CODE_MODEL
        }

    def ensure_directories(self) -> None:
        """确保必要的目录存在"""
        import os
        directories = [
            self.UPLOAD_DIR,
            self.STATIC_DIR,
            self.SCREENSHOT_DIR,
            self.TEMP_DIR,
            self.AUTOGEN_CACHE_DIR,
            self.AUTOGEN_WORK_DIR,
            os.path.dirname(self.LOG_FILE)
        ]

        for directory in directories:
            os.makedirs(directory, exist_ok=True)

    class Config:
        # 指定.env文件路径
        env_file = "app/core/.env"
        case_sensitive = True
        # 允许从环境变量中读取配置
        env_prefix = ""
        # 编码设置
        env_file_encoding = 'utf-8'


# 全局配置实例
settings = Settings()

# 初始化时验证配置和创建目录
try:
    settings.validate_ai_models()
    settings.ensure_directories()
except Exception as e:
    import warnings
    warnings.warn(f"配置验证或目录创建失败: {e}")


def get_settings() -> Settings:
    """获取配置实例"""
    return settings


def reload_settings() -> Settings:
    """重新加载配置"""
    global settings
    settings = Settings()
    settings.validate_ai_models()
    settings.ensure_directories()
    return settings
