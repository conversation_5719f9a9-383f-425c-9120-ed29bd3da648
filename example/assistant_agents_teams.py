import asyncio

from autogen_agentchat.agents import Assistant<PERSON><PERSON>
from autogen_agentchat.base import TaskResult
from autogen_agentchat.conditions import ExternalTermination, TextMentionTermination
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_agentchat.ui import Console
from autogen_core import CancellationToken
from autogen_ext.models.openai import OpenAIChatCompletionClient

# Create an OpenAI model client.
#

from llms import deepseek_model_client

# Create the primary agent.
primary_agent = AssistantAgent(
    "primary",
    model_client=deepseek_model_client(),
    system_message="你是一位擅长写七言古诗的诗人",
)

# Create the critic agent.
critic_agent = AssistantAgent(
    "critic",
    model_client=deepseek_model_client(),
    system_message="你是一位擅长评审诗歌的文学评论家，当你认为诗歌写得很好时，请回复APPROVE",
)

# Define a termination condition that stops the task if the critic approves.
text_termination = TextMentionTermination("APPROVE")

# Create a team with the primary and critic agents.
team = RoundRobinGroupChat([primary_agent, critic_agent], termination_condition=text_termination)


async def main():
    # Use `asyncio.run(...)` when running in a script.
    result = await team.run(task="帮我写一首七言古诗，题目是《秋思》")
    print(result)

async def stream_main():
    # When running inside a script, use a async main function and call it from `asyncio.run(...)`.
    await team.reset()  # Reset the team for a new task.
    async for message in team.run_stream(task="Write a short poem about the fall season."):  # type: ignore
        if isinstance(message, TaskResult):
            print("Stop Reason:", message.stop_reason)
        else:
            print(message)


if __name__ == '__main__':
    asyncio.run(stream_main())
