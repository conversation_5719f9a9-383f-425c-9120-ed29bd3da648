from openai import OpenAI
client = OpenAI(
    api_key='sk_73a4950b1fe27d5d73b61b33a88aa2870474b9d81c857d6331e60a0',
    base_url="https://hk-intra-paas.transsion.com/tranai-proxy/v1"
)

completion = client.chat.completions.create(
  model="deepseek-v3",
  messages=[
    {"role": "system", "content": "You are a helpful assistant."},
    {"role": "user", "content": "请介绍一下AIGC"}
  ]
)

print(completion.choices[0].message.content)
