import asyncio

from autogen_agentchat.agents import <PERSON><PERSON><PERSON>
from autogen_agentchat.base import TaskR<PERSON><PERSON>
from autogen_agentchat.teams import Di<PERSON>raph<PERSON>uilder, GraphFlow
from autogen_ext.models.openai import OpenAIChatCompletionClient

# Create an OpenAI model client
from llms import deepseek_model_client

client = deepseek_model_client()

# Create the writer agent
writer = AssistantAgent("writer", model_client=client, system_message="Draft a short paragraph on climate change.")

# Create the reviewer agent
reviewer = AssistantAgent("reviewer", model_client=client, system_message="Review the draft and suggest improvements.")

# Build the graph
builder = DiGraphBuilder()
builder.add_node(writer).add_node(reviewer)
builder.add_edge(writer, reviewer)

# Build and validate the graph
graph = builder.build()

# Create the flow
flow = GraphFlow([writer, reviewer], graph=graph)


async def main():
    # Use `asyncio.run(...)` when running in a script.
    result = await flow.run(task="Write a short paragraph on climate change.")
    print(result)

async def stream_main():
    # When running inside a script, use a async main function and call it from `asyncio.run(...)`.
    async for message in flow.run_stream(task="Write a short poem about the fall season."):  # type: ignore
        if isinstance(message, TaskResult):
            print("Stop Reason:", message.stop_reason)
        else:
            print(message)

if __name__ == '__main__':
    asyncio.run(stream_main())
