from autogen_ext.models.openai import OpenAIChatCompletionClient


def deepseek_model_client() -> OpenAIChatCompletionClient:
    model_client = OpenAIChatCompletionClient(
        model="deepseek-v3",
        api_key="sk_73a4950b1fe27d5d73b61b33a88aa2870474b9d81c857d6331e60a0",
        base_url="https://hk-intra-paas.transsion.com/tranai-proxy/v1",
        model_info={
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "family": "unknown",
            "multiple_system_messages": True
        }
    )
    return model_client

def qwenvl_model_client() -> OpenAIChatCompletionClient:
    model_client = OpenAIChatCompletionClient(
        model="qwen-vl-max-latest",
        api_key="sk_73a4950b1fe27d5d73b61b33a88aa2870474b9d81c857d6331e60a0",
        base_url="https://hk-intra-paas.transsion.com/tranai-proxy/v1",
        model_info={
            "vision": True,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "family": "unknown",
            "multiple_system_messages": True
        }
    )
    return model_client

def uitars_model_client()-> OpenAIChatCompletionClient:
    model_client = OpenAIChatCompletionClient(
        model="doubao-1-5-ui-tars-250428",
        api_key="7cd14776-e901-4875-868d-e01ee77a4eb2",
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        model_info={
            "vision": True,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "family": "unknown",
            "multiple_system_messages": True
        }
    )
    return model_client

def openai_model_client()-> OpenAIChatCompletionClient:
    model_client = OpenAIChatCompletionClient(
        model="gpt-4o",
        api_key="sk_73a4950b1fe27d5d73b61b33a88aa2870474b9d81c857d6331e60a0",
        base_url="https://hk-intra-paas.transsion.com/tranai-proxy/v1",
        model_info={
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "family": "unknown",
            "multiple_system_messages": True
        }
    )
    return model_client
