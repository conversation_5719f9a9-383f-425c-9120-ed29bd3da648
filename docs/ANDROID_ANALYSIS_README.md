# Android页面分析智能体

基于adb、uiautomator2和UI_TARS模型的Android页面分析智能体，能够自动完成应用启动、页面导航、DOM捕获、截图和智能分析的完整流程。

## 🚀 功能特性

### 核心功能
1. **启动指定APP** - 支持包名和Activity启动
2. **导航到对应页面** - 支持点击、输入、滑动等操作
3. **获取当前页面DOM树信息** - 完整的UI层次结构
4. **截取当前页面截图** - 高质量PNG格式
5. **UI_TARS智能分析** - 基于视觉和DOM的页面元素分析
6. **JSON格式数据输出** - 结构化的分析结果

### 技术栈
- **Android自动化**: uiautomator2 + adbutils
- **AI分析**: UI_TARS模型 (doubao-1-5-ui-tars-250428)
- **后端框架**: FastAPI
- **图像处理**: Pillow

## 📱 测试结果

✅ **服务状态**: healthy  
✅ **设备连接**: 1个设备已连接 (13764254B4001229: CM8-OP)  
✅ **截图功能**: 正常 (1080x2400分辨率)  
✅ **DOM捕获**: 正常  

## 🔧 API接口

### 1. 健康检查
```http
GET /api/v1/android/health
```

### 2. 获取设备列表
```http
GET /api/v1/android/devices
```

### 3. 完整页面分析
```http
POST /api/v1/android/analyze
Content-Type: application/json

{
  "app_launch": {
    "package_name": "com.example.app",
    "activity_name": "com.example.app.MainActivity",
    "device_id": null,
    "wait_timeout": 10
  },
  "navigation": {
    "actions": [
      {
        "type": "click",
        "resource_id": "com.example.app:id/button_login"
      },
      {
        "type": "input",
        "resource_id": "com.example.app:id/edit_username",
        "text": "testuser"
      },
      {
        "type": "wait",
        "time": 2
      }
    ]
  },
  "analysis_config": {
    "type": "full",
    "custom_prompt": "请特别关注登录相关的UI元素"
  }
}
```

### 4. 分析当前页面
```http
POST /api/v1/android/analyze/current?analysis_type=full
```

### 5. 启动应用
```http
POST /api/v1/android/launch
Content-Type: application/json

{
  "package_name": "com.android.settings",
  "wait_timeout": 10
}
```

### 6. 页面导航
```http
POST /api/v1/android/navigate
Content-Type: application/json

{
  "actions": [
    {
      "type": "click",
      "text": "设置"
    },
    {
      "type": "swipe",
      "direction": "up"
    }
  ]
}
```

### 7. 截图
```http
GET /api/v1/android/capture/screenshot
```

### 8. DOM捕获
```http
GET /api/v1/android/capture/dom
```

## 📊 响应格式

### 完整分析响应示例
```json
{
  "success": true,
  "timestamp": "2025-08-26T17:17:25.123456",
  "execution_time": 15.67,
  "device_info": {
    "device_id": "13764254B4001229",
    "device_name": "CM8-OP",
    "platform_version": "11",
    "screen_size": "1080x2400"
  },
  "app_info": {
    "package": "com.example.app",
    "activity": "com.example.app.MainActivity"
  },
  "page_analysis": {
    "page_title": "登录页面",
    "page_type": "login",
    "main_content": "用户登录界面",
    "analysis_summary": "这是一个标准的登录页面...",
    "confidence_score": 0.95,
    "ui_elements": [
      {
        "element_type": "button",
        "text_content": "登录",
        "description": "主要登录按钮",
        "position": {"left": 100, "top": 200, "right": 300, "bottom": 250},
        "functionality": "执行用户登录操作",
        "interaction_type": "button",
        "importance_score": 0.9,
        "attributes": {
          "clickable": true,
          "enabled": true
        }
      }
    ],
    "layout_structure": {
      "main_sections": ["header", "form", "footer"],
      "form_elements": 3
    },
    "accessibility_info": {
      "total_elements": 25,
      "clickable_elements": 5,
      "text_elements": 8
    }
  },
  "technical_info": {
    "dom_elements_count": 25,
    "screenshot_path": "static/screenshots/screenshot_20250826_171725_067.png",
    "screenshot_size": {
      "width": 1080,
      "height": 2400
    }
  }
}
```

## 🎯 支持的操作类型

### 导航操作
- **click**: 点击操作
  - `resource_id`: 通过资源ID点击
  - `text`: 通过文本内容点击
  - `coordinates`: 通过坐标点击
- **input**: 输入操作
  - `resource_id`: 目标输入框资源ID
  - `text`: 要输入的文本
- **swipe**: 滑动操作
  - `direction`: up/down/left/right
- **wait**: 等待操作
  - `time`: 等待时间(秒)

## 🔍 UI_TARS分析能力

### 分析类型
- **full**: 完整分析 (默认)
- **detailed**: 详细分析
- **summary**: 简要分析

### 识别元素类型
- 按钮 (button)
- 输入框 (input)
- 文本 (text)
- 图片 (image)
- 列表 (list)
- 导航 (navigation)
- 菜单 (menu)

### 分析维度
- 元素功能描述
- 交互类型识别
- 重要性评分
- 位置信息
- 布局结构
- 无障碍信息

## 🚀 快速开始

### 1. 连接Android设备
确保Android设备已连接并开启USB调试

### 2. 启动服务
```bash
cd backend
python -c "import uvicorn; uvicorn.run('app.main:app', host='0.0.0.0', port=8001)"
```

### 3. 检查设备连接
```bash
curl http://localhost:8001/api/v1/android/devices
```

### 4. 分析当前页面
```bash
curl -X POST http://localhost:8001/api/v1/android/analyze/current
```

## 📝 使用示例

### Python示例
```python
import requests

# 1. 启动应用
launch_data = {
    "package_name": "com.android.settings",
    "wait_timeout": 10
}
response = requests.post("http://localhost:8001/api/v1/android/launch", json=launch_data)

# 2. 分析当前页面
response = requests.post("http://localhost:8001/api/v1/android/analyze/current")
result = response.json()

print(f"页面标题: {result['page_analysis']['page_title']}")
print(f"UI元素数量: {len(result['page_analysis']['ui_elements'])}")
```

## ⚠️ 注意事项

1. **设备要求**: Android 5.0+ (API 21+)
2. **权限要求**: USB调试权限
3. **网络要求**: 需要访问UI_TARS API
4. **性能**: 完整分析可能需要10-30秒
5. **存储**: 截图文件会保存在static/screenshots目录

## 🔧 故障排除

### 常见问题
1. **设备连接失败**: 检查USB调试是否开启
2. **应用启动失败**: 确认包名正确且应用已安装
3. **分析超时**: 检查网络连接和UI_TARS API配置
4. **截图失败**: 确认设备屏幕处于唤醒状态

### 日志查看
服务日志会显示详细的执行信息，包括设备连接、操作执行和分析过程。

## 🎉 总结

Android页面分析智能体已经成功实现了完整的功能链路：

✅ **设备管理** - 自动发现和连接Android设备  
✅ **应用控制** - 启动应用和页面导航  
✅ **数据捕获** - DOM结构和屏幕截图  
✅ **智能分析** - UI_TARS模型深度分析  
✅ **结果输出** - 结构化JSON格式  

系统现在可以为UI自动化测试提供强大的页面分析能力！
