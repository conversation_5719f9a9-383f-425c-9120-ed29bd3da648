# UI自动化测试系统后端

基于FastAPI和AutoGen 0.5.7的UI自动化测试系统后端服务，支持SSE流式输出和智能对话。

## 功能特性

- 🚀 基于FastAPI的高性能异步API
- 🤖 集成AutoGen 0.5.7智能体框架
- 📡 支持SSE（Server-Sent Events）流式输出
- 💬 实时聊天对话功能
- 🔧 完整的配置管理
- 📝 详细的API文档

## 技术栈

- **框架**: FastAPI 0.104+
- **智能体**: AutoGen Core 0.5.7
- **流式输出**: SSE (Server-Sent Events)
- **异步支持**: asyncio, aiofiles
- **日志**: loguru
- **配置管理**: pydantic-settings

## 快速开始

### 1. 安装依赖

```bash
cd backend
pip install -r requirements.txt
```

### 2. 配置环境变量

复制环境变量示例文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置必要的参数：
```env
DEBUG=false
HOST=0.0.0.0
PORT=8000

# AI模型配置（可选）
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo
```

### 3. 启动服务

```bash
# 方式1：使用启动脚本
python run.py

# 方式2：直接使用uvicorn
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# 方式3：使用Python模块
python -m app.main
```

### 4. 访问API文档

启动后访问以下地址：
- API文档: http://localhost:8000/api/v1/docs
- ReDoc文档: http://localhost:8000/api/v1/redoc
- 健康检查: http://localhost:8000/health

## API接口

### 聊天接口

#### 1. 发送消息（非流式）
```http
POST /api/v1/chat
Content-Type: application/json

{
    "message": "你好，请帮我生成一个Android自动化测试脚本",
    "conversation_id": "optional-conversation-id",
    "stream": false
}
```

#### 2. 流式聊天（推荐）
```http
POST /api/v1/chat/stream
Content-Type: application/json

{
    "message": "你好，请帮我生成一个Android自动化测试脚本",
    "conversation_id": "optional-conversation-id",
    "stream": true
}
```

响应格式（SSE）：
```
event: connected
data: {"type": "connected", "conversation_id": "xxx", "message": "连接已建立"}

event: message_chunk
data: {"type": "chunk", "content": "你好！我是UI自动化", "conversation_id": "xxx", "is_complete": false}

event: message_complete
data: {"type": "complete", "content": "完整的响应内容", "conversation_id": "xxx", "is_complete": true}
```

#### 3. 获取对话历史
```http
GET /api/v1/conversations/{conversation_id}/history
```

#### 4. 删除对话
```http
DELETE /api/v1/conversations/{conversation_id}
```

## 项目结构

```
backend/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI主应用
│   ├── api/                 # API路由
│   │   ├── __init__.py
│   │   ├── api.py          # 路由汇总
│   │   └── chat.py         # 聊天API
│   ├── core/               # 核心配置
│   │   ├── __init__.py
│   │   └── config.py       # 应用配置
│   ├── models/             # 数据模型
│   │   ├── __init__.py
│   │   └── chat.py         # 聊天模型
│   ├── services/           # 业务服务
│   │   ├── __init__.py
│   │   └── autogen_service.py  # AutoGen服务
│   └── utils/              # 工具模块
│       ├── __init__.py
│       └── sse.py          # SSE工具
├── requirements.txt        # 依赖列表
├── .env.example           # 环境变量示例
├── run.py                 # 启动脚本
└── README.md              # 说明文档
```

## 开发指南

### 添加新的智能体

1. 在 `app/services/autogen_service.py` 中创建新的智能体类：

```python
@type_subscription(topic_type="your_topic")
class YourAgent(RoutedAgent):
    def __init__(self):
        super().__init__("你的智能体名称")
    
    @message_handler
    async def handle_message(self, message: YourMessage, ctx: MessageContext):
        # 处理消息逻辑
        pass
```

2. 在服务中注册智能体：

```python
await YourAgent.register(runtime, "agent_id", lambda: YourAgent())
```

### 自定义SSE事件

在 `app/utils/sse.py` 中扩展SSE功能：

```python
# 发送自定义事件
await sse_manager.send_to_connection(
    connection_id,
    {"custom_data": "value"},
    "custom_event"
)
```

## 部署

### Docker部署

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY ../backend/requirements.txt .
RUN pip install -r requirements.txt

COPY ../backend .
EXPOSE 8000

CMD ["python", "run.py"]
```

### 生产环境

```bash
# 使用gunicorn部署
pip install gunicorn
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

## 故障排除

### 常见问题

1. **AutoGen初始化失败**
   - 检查Python版本是否为3.8+
   - 确认autogen-core版本为0.5.7

2. **SSE连接断开**
   - 检查网络连接
   - 确认防火墙设置

3. **依赖安装失败**
   - 升级pip: `pip install --upgrade pip`
   - 使用国内镜像: `pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt`

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 许可证

MIT License
